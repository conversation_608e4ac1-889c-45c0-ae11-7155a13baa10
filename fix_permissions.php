<?php
/**
 * سكريپت إصلاح صلاحيات الملفات
 */

echo "<h1>إصلاح صلاحيات الملفات</h1>";

// المجلدات التي تحتاج صلاحيات كتابة
$writableDirectories = [
    'database',
    'database/data',
    'uploads',
    'uploads/activities',
    'uploads/news'
];

// الملفات التي تحتاج صلاحيات كتابة
$writableFiles = [
    'database/data/activities.json',
    'database/data/admin_users.json'
];

echo "<h2>فحص وإصلاح صلاحيات المجلدات:</h2>";

foreach ($writableDirectories as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir);
        
        echo "<p><strong>$dir</strong> - صلاحيات: $perms - " . ($writable ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة') . "</p>";
        
        if (!$writable) {
            // محاولة إصلاح الصلاحيات
            if (chmod($dir, 0777)) {
                echo "<p style='color: green;'>✅ تم إصلاح صلاحيات $dir</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إصلاح صلاحيات $dir</p>";
                echo "<p>قم بتشغيل الأمر: <code>chmod 777 $dir</code></p>";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠️ المجلد $dir غير موجود</p>";
        
        // محاولة إنشاء المجلد
        if (mkdir($dir, 0777, true)) {
            echo "<p style='color: green;'>✅ تم إنشاء المجلد $dir</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء المجلد $dir</p>";
        }
    }
}

echo "<h2>فحص وإصلاح صلاحيات الملفات:</h2>";

foreach ($writableFiles as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        $writable = is_writable($file);
        
        echo "<p><strong>$file</strong> - صلاحيات: $perms - " . ($writable ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة') . "</p>";
        
        if (!$writable) {
            if (chmod($file, 0666)) {
                echo "<p style='color: green;'>✅ تم إصلاح صلاحيات $file</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إصلاح صلاحيات $file</p>";
                echo "<p>قم بتشغيل الأمر: <code>chmod 666 $file</code></p>";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠️ الملف $file غير موجود</p>";
        
        // محاولة إنشاء الملف
        $dir = dirname($file);
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
        
        if (file_put_contents($file, '[]')) {
            chmod($file, 0666);
            echo "<p style='color: green;'>✅ تم إنشاء الملف $file</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء الملف $file</p>";
        }
    }
}

// اختبار الكتابة
echo "<h2>اختبار الكتابة:</h2>";

$testFile = 'database/data/test_write.txt';
$testContent = 'Test write permission - ' . date('Y-m-d H:i:s');

if (file_put_contents($testFile, $testContent)) {
    echo "<p style='color: green;'>✅ اختبار الكتابة نجح</p>";
    unlink($testFile); // حذف ملف الاختبار
} else {
    echo "<p style='color: red;'>❌ اختبار الكتابة فشل</p>";
    echo "<p>تحتاج لتشغيل الأوامر التالية عبر SSH:</p>";
    echo "<pre>";
    echo "chmod 777 database/\n";
    echo "chmod 777 database/data/\n";
    echo "chmod 777 uploads/\n";
    echo "chmod 777 uploads/activities/\n";
    echo "chmod 777 uploads/news/\n";
    echo "chown -R www-data:www-data database/\n";
    echo "chown -R www-data:www-data uploads/";
    echo "</pre>";
}

echo "<h2>أوامر SSH المطلوبة:</h2>";
echo "<p>إذا لم تنجح الإصلاحات التلقائية، قم بتشغيل هذه الأوامر عبر SSH:</p>";
echo "<pre>";
echo "cd " . $_SERVER['DOCUMENT_ROOT'] . "\n";
echo "chmod 777 database/\n";
echo "chmod 777 database/data/\n";
echo "chmod 777 uploads/\n";
echo "chmod 777 uploads/activities/\n";
echo "chmod 777 uploads/news/\n";
echo "chmod 666 database/data/*.json\n";
echo "chown -R www-data:www-data database/\n";
echo "chown -R www-data:www-data uploads/\n";
echo "</pre>";

echo "<p style='color: red;'><strong>ملاحظة:</strong> احذف هذا الملف بعد إصلاح المشكلة!</p>";
echo "<p><a href='server_setup.php'>العودة لإعداد الخادم</a></p>";
?>
