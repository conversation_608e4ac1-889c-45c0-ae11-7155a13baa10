<?php
// Debug version of activities page
ini_set('display_errors', 1);
error_reporting(E_ALL);
session_start(); 

echo "<h1>Debug Activities Page</h1>";

// Test including activities manager
echo "<h2>1. Including activities manager</h2>";
try {
    require_once __DIR__ . '/admin/activities_manager.php';
    echo "<p style='color: green;'>✅ Activities manager included successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error including activities manager: " . $e->getMessage() . "</p>";
    exit;
}

// Test getting activities
echo "<h2>2. Getting activities</h2>";
try {
    $activities = getActivities();
    echo "<p style='color: green;'>✅ getActivities() called successfully</p>";
    echo "<p>Number of activities: " . count($activities) . "</p>";
    
    if (!empty($activities)) {
        echo "<h3>First activity:</h3>";
        echo "<pre>" . print_r($activities[0], true) . "</pre>";
    } else {
        echo "<p style='color: orange;'>⚠️ No activities returned</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error getting activities: " . $e->getMessage() . "</p>";
}

// Test getting featured activities
echo "<h2>3. Getting featured activities</h2>";
try {
    $featuredActivities = getFeaturedActivities();
    echo "<p style='color: green;'>✅ getFeaturedActivities() called successfully</p>";
    echo "<p>Number of featured activities: " . count($featuredActivities) . "</p>";
    
    if (!empty($featuredActivities)) {
        echo "<h3>Featured activities:</h3>";
        foreach ($featuredActivities as $activity) {
            echo "<p>- " . $activity['title'] . " (Featured: " . ($activity['featured'] ? 'Yes' : 'No') . ")</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error getting featured activities: " . $e->getMessage() . "</p>";
}

// Test getting stats
echo "<h2>4. Getting statistics</h2>";
try {
    $activitiesStats = getActivitiesStats();
    echo "<p style='color: green;'>✅ getActivitiesStats() called successfully</p>";
    echo "<pre>" . print_r($activitiesStats, true) . "</pre>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error getting statistics: " . $e->getMessage() . "</p>";
}

// Test database connection directly
echo "<h2>5. Testing database connection directly</h2>";
try {
    $connection = getDatabaseConnection();
    if ($connection) {
        echo "<p style='color: green;'>✅ Database connection successful</p>";
        echo "<p>Connection type: " . get_class($connection) . "</p>";
        
        // Test query
        if ($connection instanceof PDO) {
            $stmt = $connection->query("SELECT COUNT(*) as count FROM activities");
            $result = $stmt->fetch();
            echo "<p>Activities in database: " . $result['count'] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection error: " . $e->getMessage() . "</p>";
}

// Test JSON file directly
echo "<h2>6. Testing JSON file directly</h2>";
$jsonFile = __DIR__ . '/database/data/activities.json';
if (file_exists($jsonFile)) {
    echo "<p style='color: green;'>✅ JSON file exists</p>";
    $jsonContent = file_get_contents($jsonFile);
    $jsonData = json_decode($jsonContent, true);
    if ($jsonData) {
        echo "<p style='color: green;'>✅ JSON file is valid</p>";
        echo "<p>Activities in JSON: " . count($jsonData) . "</p>";
        
        if (!empty($jsonData)) {
            echo "<h3>First JSON activity:</h3>";
            echo "<pre>" . print_r($jsonData[0], true) . "</pre>";
        }
    } else {
        echo "<p style='color: red;'>❌ JSON file is invalid</p>";
    }
} else {
    echo "<p style='color: red;'>❌ JSON file not found: $jsonFile</p>";
}

echo "<h2>Debug Complete</h2>";
?>
