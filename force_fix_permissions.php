<?php
/**
 * إصلاح قسري للصلاحيات - الحل النهائي
 */

echo "<h1>الإصلاح القسري للصلاحيات</h1>";

// إنشاء مجلد بديل في tmp
$tempDir = '/tmp/ngo_data';
$tempFile = '/tmp/ngo_activities.json';

echo "<h2>1. إنشاء مجلد بديل في /tmp</h2>";

if (!is_dir($tempDir)) {
    if (mkdir($tempDir, 0777, true)) {
        echo "<p style='color: green;'>✅ تم إنشاء مجلد بديل: $tempDir</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء مجلد بديل</p>";
    }
} else {
    echo "<p style='color: green;'>✅ المجلد البديل موجود: $tempDir</p>";
}

// إنشاء ملف activities.json في tmp
if (!file_exists($tempFile)) {
    $sampleData = [
        [
            "id" => 1,
            "title" => "توزيع المساعدات الغذائية",
            "description" => "توزيع سلل غذائية على العائلات المحتاجة",
            "content" => "تم تنظيم حملة لتوزيع السلل الغذائية على 100 عائلة محتاجة في منطقة حي الكفاءات.",
            "activity_date" => "2024-12-15",
            "location" => "حي الكفاءات الثانية - الموصل",
            "beneficiaries_count" => 100,
            "budget" => 2500000,
            "status" => "completed",
            "category" => "food",
            "featured" => true,
            "image" => "",
            "created_at" => "2024-12-15 10:00:00",
            "updated_at" => "2024-12-15 10:00:00"
        ],
        [
            "id" => 2,
            "title" => "حملة التبرع بالدم",
            "description" => "تنظيم حملة للتبرع بالدم لصالح مستشفيات المدينة",
            "content" => "نظمت الرابطة حملة للتبرع بالدم بالتعاون مع بنك الدم المركزي في الموصل.",
            "activity_date" => "2024-12-10",
            "location" => "مركز الرابطة - الموصل",
            "beneficiaries_count" => 80,
            "budget" => 500000,
            "status" => "completed",
            "category" => "health",
            "featured" => true,
            "image" => "",
            "created_at" => "2024-12-10 09:00:00",
            "updated_at" => "2024-12-10 09:00:00"
        ]
    ];
    
    if (file_put_contents($tempFile, json_encode($sampleData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT))) {
        echo "<p style='color: green;'>✅ تم إنشاء ملف البيانات البديل: $tempFile</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء ملف البيانات البديل</p>";
    }
} else {
    echo "<p style='color: green;'>✅ ملف البيانات البديل موجود: $tempFile</p>";
}

// اختبار قراءة وكتابة الملف البديل
echo "<h2>2. اختبار الملف البديل</h2>";

if (is_readable($tempFile)) {
    echo "<p style='color: green;'>✅ الملف البديل قابل للقراءة</p>";
    
    $content = file_get_contents($tempFile);
    $data = json_decode($content, true);
    if ($data) {
        echo "<p style='color: green;'>✅ تم قراءة " . count($data) . " نشاط من الملف البديل</p>";
    }
} else {
    echo "<p style='color: red;'>❌ الملف البديل غير قابل للقراءة</p>";
}

if (is_writable($tempFile)) {
    echo "<p style='color: green;'>✅ الملف البديل قابل للكتابة</p>";
    
    // اختبار الكتابة
    $testData = ["test" => "write test " . time()];
    $currentData = json_decode(file_get_contents($tempFile), true);
    $currentData[] = $testData;
    
    if (file_put_contents($tempFile, json_encode($currentData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT))) {
        echo "<p style='color: green;'>✅ اختبار الكتابة نجح</p>";
        
        // إزالة البيانات التجريبية
        array_pop($currentData);
        file_put_contents($tempFile, json_encode($currentData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
    } else {
        echo "<p style='color: red;'>❌ اختبار الكتابة فشل</p>";
    }
} else {
    echo "<p style='color: red;'>❌ الملف البديل غير قابل للكتابة</p>";
}

// محاولة إصلاح المجلد الأصلي
echo "<h2>3. محاولة إصلاح المجلد الأصلي</h2>";

$originalDir = 'database/data';
$originalFile = 'database/data/activities.json';

// إنشاء المجلد إذا لم يكن موجوداً
if (!is_dir('database')) {
    mkdir('database', 0777, true);
}
if (!is_dir($originalDir)) {
    mkdir($originalDir, 0777, true);
}

// محاولة تغيير الصلاحيات
if (chmod('database', 0777)) {
    echo "<p style='color: green;'>✅ تم تغيير صلاحيات مجلد database</p>";
} else {
    echo "<p style='color: orange;'>⚠️ لم يتم تغيير صلاحيات مجلد database</p>";
}

if (chmod($originalDir, 0777)) {
    echo "<p style='color: green;'>✅ تم تغيير صلاحيات مجلد database/data</p>";
} else {
    echo "<p style='color: orange;'>⚠️ لم يتم تغيير صلاحيات مجلد database/data</p>";
}

// نسخ البيانات من الملف البديل إلى الملف الأصلي
if (file_exists($tempFile) && is_writable($originalDir)) {
    $tempContent = file_get_contents($tempFile);
    if (file_put_contents($originalFile, $tempContent)) {
        chmod($originalFile, 0666);
        echo "<p style='color: green;'>✅ تم نسخ البيانات إلى الملف الأصلي</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في نسخ البيانات إلى الملف الأصلي</p>";
    }
}

// اختبار النظام
echo "<h2>4. اختبار النظام</h2>";

try {
    require_once 'admin/activities_manager.php';
    
    $activities = getActivities();
    echo "<p style='color: green;'>✅ تم تحميل " . count($activities) . " نشاط</p>";
    
    $featuredActivities = getFeaturedActivities();
    echo "<p style='color: green;'>✅ تم تحميل " . count($featuredActivities) . " نشاط مميز</p>";
    
    $stats = getActivitiesStats();
    echo "<p style='color: green;'>✅ تم حساب الإحصائيات: " . $stats['total'] . " نشاط إجمالي</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار النظام: " . $e->getMessage() . "</p>";
}

// أوامر SSH النهائية
echo "<h2>5. أوامر SSH النهائية (إذا لم تنجح الحلول السابقة)</h2>";
echo "<p>قم بتشغيل هذه الأوامر عبر SSH:</p>";
echo "<pre style='background: #f0f0f0; padding: 10px;'>";
echo "# الانتقال إلى مجلد الموقع\n";
echo "cd " . getcwd() . "\n\n";
echo "# حذف وإعادة إنشاء المجلدات\n";
echo "sudo rm -rf database/data/\n";
echo "sudo mkdir -p database/data/\n";
echo "sudo chmod 777 database/\n";
echo "sudo chmod 777 database/data/\n\n";
echo "# نسخ البيانات من الملف البديل\n";
echo "sudo cp /tmp/ngo_activities.json database/data/activities.json\n";
echo "sudo chmod 666 database/data/activities.json\n\n";
echo "# تغيير المالك\n";
echo "sudo chown -R www-data:www-data database/\n";
echo "sudo chown -R www-data:www-data uploads/\n\n";
echo "# إذا لم ينجح www-data، جرب:\n";
echo "sudo chown -R apache:apache database/\n";
echo "sudo chown -R nginx:nginx database/\n";
echo "</pre>";

echo "<h2>✅ انتهى الإصلاح القسري</h2>";
echo "<p><a href='activities.php'>اختبار صفحة النشاطات</a></p>";
echo "<p><a href='test_add_activity.php'>اختبار إضافة نشاط</a></p>";
echo "<p style='color: red;'><strong>احذف هذا الملف بعد حل المشكلة!</strong></p>";
?>
