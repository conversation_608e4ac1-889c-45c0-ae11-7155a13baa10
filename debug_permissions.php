<?php
/**
 * تشخيص متقدم لمشاكل الصلاحيات
 */

echo "<h1>تشخيص متقدم للصلاحيات</h1>";

// معلومات المستخدم الحالي
echo "<h2>معلومات المستخدم:</h2>";
echo "<p><strong>PHP User:</strong> " . get_current_user() . "</p>";
echo "<p><strong>Process User:</strong> " . posix_getpwuid(posix_geteuid())['name'] . "</p>";
echo "<p><strong>Process Group:</strong> " . posix_getgrgid(posix_getegid())['name'] . "</p>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";

// فحص المجلدات بالتفصيل
$directories = ['database', 'database/data', 'uploads', 'uploads/activities'];

echo "<h2>تحليل تفصيلي للمجلدات:</h2>";

foreach ($directories as $dir) {
    echo "<h3>المجلد: $dir</h3>";
    
    if (file_exists($dir)) {
        $stat = stat($dir);
        $perms = fileperms($dir);
        $owner = posix_getpwuid($stat['uid']);
        $group = posix_getgrgid($stat['gid']);
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><td><strong>موجود</strong></td><td style='color: green;'>✅ نعم</td></tr>";
        echo "<tr><td><strong>الصلاحيات (رقمي)</strong></td><td>" . substr(sprintf('%o', $perms), -4) . "</td></tr>";
        echo "<tr><td><strong>الصلاحيات (نصي)</strong></td><td>" . substr(sprintf('%o', $perms), -3) . "</td></tr>";
        echo "<tr><td><strong>المالك</strong></td><td>" . ($owner['name'] ?? 'غير معروف') . " (UID: " . $stat['uid'] . ")</td></tr>";
        echo "<tr><td><strong>المجموعة</strong></td><td>" . ($group['name'] ?? 'غير معروف') . " (GID: " . $stat['gid'] . ")</td></tr>";
        echo "<tr><td><strong>قابل للقراءة</strong></td><td>" . (is_readable($dir) ? '✅ نعم' : '❌ لا') . "</td></tr>";
        echo "<tr><td><strong>قابل للكتابة</strong></td><td>" . (is_writable($dir) ? '✅ نعم' : '❌ لا') . "</td></tr>";
        echo "<tr><td><strong>قابل للتنفيذ</strong></td><td>" . (is_executable($dir) ? '✅ نعم' : '❌ لا') . "</td></tr>";
        echo "</table>";
        
        // اختبار الكتابة
        $testFile = $dir . '/test_' . time() . '.txt';
        if (file_put_contents($testFile, 'test')) {
            echo "<p style='color: green;'>✅ اختبار الكتابة نجح</p>";
            unlink($testFile);
        } else {
            echo "<p style='color: red;'>❌ اختبار الكتابة فشل</p>";
            
            // تحليل سبب الفشل
            $error = error_get_last();
            if ($error) {
                echo "<p><strong>رسالة الخطأ:</strong> " . $error['message'] . "</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ المجلد غير موجود</p>";
        
        // محاولة إنشاء المجلد
        if (mkdir($dir, 0777, true)) {
            echo "<p style='color: green;'>✅ تم إنشاء المجلد</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء المجلد</p>";
        }
    }
    
    echo "<hr>";
}

// فحص ملف activities.json
echo "<h2>فحص ملف activities.json:</h2>";
$jsonFile = 'database/data/activities.json';

if (file_exists($jsonFile)) {
    $stat = stat($jsonFile);
    $perms = fileperms($jsonFile);
    $owner = posix_getpwuid($stat['uid']);
    $group = posix_getgrgid($stat['gid']);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><td><strong>موجود</strong></td><td style='color: green;'>✅ نعم</td></tr>";
    echo "<tr><td><strong>الحجم</strong></td><td>" . filesize($jsonFile) . " بايت</td></tr>";
    echo "<tr><td><strong>الصلاحيات</strong></td><td>" . substr(sprintf('%o', $perms), -4) . "</td></tr>";
    echo "<tr><td><strong>المالك</strong></td><td>" . ($owner['name'] ?? 'غير معروف') . "</td></tr>";
    echo "<tr><td><strong>المجموعة</strong></td><td>" . ($group['name'] ?? 'غير معروف') . "</td></tr>";
    echo "<tr><td><strong>قابل للقراءة</strong></td><td>" . (is_readable($jsonFile) ? '✅ نعم' : '❌ لا') . "</td></tr>";
    echo "<tr><td><strong>قابل للكتابة</strong></td><td>" . (is_writable($jsonFile) ? '✅ نعم' : '❌ لا') . "</td></tr>";
    echo "</table>";
    
    // اختبار قراءة الملف
    $content = file_get_contents($jsonFile);
    if ($content !== false) {
        $data = json_decode($content, true);
        echo "<p style='color: green;'>✅ تم قراءة الملف بنجاح (" . count($data) . " عنصر)</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في قراءة الملف</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ ملف activities.json غير موجود</p>";
}

// أوامر الإصلاح المقترحة
echo "<h2>أوامر الإصلاح المقترحة:</h2>";
echo "<p>قم بتشغيل هذه الأوامر عبر SSH:</p>";

$currentUser = posix_getpwuid(posix_geteuid())['name'];
$webUsers = ['www-data', 'apache', 'nginx', 'httpd'];

echo "<pre style='background: #f0f0f0; padding: 10px;'>";
echo "# الانتقال إلى مجلد الموقع\n";
echo "cd " . getcwd() . "\n\n";

echo "# تغيير مالك الملفات (جرب هذه الأوامر واحداً تلو الآخر):\n";
foreach ($webUsers as $user) {
    echo "sudo chown -R $user:$user database/\n";
    echo "sudo chown -R $user:$user uploads/\n\n";
}

echo "# تعيين الصلاحيات:\n";
echo "sudo chmod 755 database/\n";
echo "sudo chmod 777 database/data/\n";
echo "sudo chmod 777 uploads/\n";
echo "sudo chmod 666 database/data/*.json\n\n";

echo "# إذا لم تنجح الطرق السابقة، استخدم:\n";
echo "sudo chmod 777 database/\n";
echo "sudo chmod 777 database/data/\n\n";

echo "# فحص النتيجة:\n";
echo "ls -la database/\n";
echo "ls -la database/data/\n";
echo "</pre>";

// معلومات إضافية
echo "<h2>معلومات إضافية:</h2>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script Path:</strong> " . __FILE__ . "</p>";
echo "<p><strong>Working Directory:</strong> " . getcwd() . "</p>";

// فحص إعدادات PHP
echo "<h2>إعدادات PHP ذات الصلة:</h2>";
echo "<p><strong>safe_mode:</strong> " . (ini_get('safe_mode') ? 'مفعل' : 'معطل') . "</p>";
echo "<p><strong>open_basedir:</strong> " . (ini_get('open_basedir') ?: 'غير محدد') . "</p>";
echo "<p><strong>file_uploads:</strong> " . (ini_get('file_uploads') ? 'مفعل' : 'معطل') . "</p>";

echo "<p style='color: red;'><strong>احذف هذا الملف بعد حل المشكلة!</strong></p>";
?>
