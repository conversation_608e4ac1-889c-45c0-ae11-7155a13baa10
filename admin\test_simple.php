<?php
require_once 'auth.php';

echo "<h2>فحص نظام المصادقة (بدون تشفير)</h2>";

// Check if users file exists and its content
$usersFile = getAdminUsersFile();
echo "<p><strong>موقع ملف البيانات:</strong> $usersFile</p>";

if (file_exists($usersFile)) {
    echo "<p style='color: green;'>✅ ملف البيانات موجود</p>";
    $content = file_get_contents($usersFile);
    echo "<p><strong>محتوى الملف:</strong></p>";
    echo "<pre>" . htmlspecialchars($content) . "</pre>";
    
    $users = json_decode($content, true);
    if ($users) {
        echo "<p style='color: green;'>✅ تم قراءة البيانات بنجاح</p>";
        echo "<p><strong>عدد المستخدمين:</strong> " . count($users) . "</p>";
        
        foreach ($users as $user) {
            echo "<p>مستخدم: " . $user['username'] . " - كلمة المرور: " . $user['password'] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في قراءة البيانات</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف البيانات غير موجود</p>";
    echo "<p>سيتم إنشاء ملف جديد...</p>";
    createDefaultAdminUser();
}

// Test authentication directly
echo "<h3>اختبار المصادقة المباشر</h3>";
$testUser = authenticateAdminFile('admin', 'admin123');

if ($testUser) {
    echo "<p style='color: green;'>✅ نجح اختبار المصادقة</p>";
    echo "<p>اسم المستخدم: " . $testUser['username'] . "</p>";
    echo "<p>كلمة المرور في الملف: " . $testUser['password'] . "</p>";
} else {
    echo "<p style='color: red;'>❌ فشل اختبار المصادقة</p>";
}

echo "<hr>";
echo "<p><a href='login.php'>🔐 انتقال لصفحة تسجيل الدخول</a></p>";
?>
