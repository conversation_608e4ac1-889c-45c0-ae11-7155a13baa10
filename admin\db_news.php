<?php
/**
 * News Database Management Functions
 */

require_once 'db_activities.php'; // For database connection

/**
 * Initialize news table
 */
function initializeNewsTable() {
    $connection = getDatabaseConnection();
    if (!$connection) return false;
    
    try {
        if ($connection instanceof PDO) {
            $sql = "
            CREATE TABLE IF NOT EXISTS news (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                summary TEXT,
                content TEXT NOT NULL,
                news_date DATE,
                status ENUM('published', 'draft', 'scheduled') DEFAULT 'published',
                featured BOOLEAN DEFAULT FALSE,
                image VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $connection->exec($sql);
        } else if ($connection instanceof mysqli) {
            $sql = "
            CREATE TABLE IF NOT EXISTS news (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                summary TEXT,
                content TEXT NOT NULL,
                news_date DATE,
                status ENUM('published', 'draft', 'scheduled') DEFAULT 'published',
                featured BOOLEAN DEFAULT FALSE,
                image VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $connection->query($sql);
        }
        return true;
    } catch (Exception $e) {
        error_log("Failed to create news table: " . $e->getMessage());
        return false;
    }
}

/**
 * Get all news from database
 */
function getNewsFromDB() {
    $connection = getDatabaseConnection();
    if (!$connection) return [];
    
    try {
        if ($connection instanceof PDO) {
            $stmt = $connection->prepare("SELECT * FROM news ORDER BY news_date DESC, created_at DESC");
            $stmt->execute();
            return $stmt->fetchAll();
        } else if ($connection instanceof mysqli) {
            $result = $connection->query("SELECT * FROM news ORDER BY news_date DESC, created_at DESC");
            if ($result) {
                return $result->fetch_all(MYSQLI_ASSOC);
            }
        }
    } catch (Exception $e) {
        error_log("Failed to get news: " . $e->getMessage());
    }
    
    return [];
}

/**
 * Add new news to database
 */
function addNewsToDB($data) {
    $connection = getDatabaseConnection();
    if (!$connection) return false;
    
    try {
        if ($connection instanceof PDO) {
            $sql = "
            INSERT INTO news (
                title, summary, content, news_date, status, featured, image
            ) VALUES (
                :title, :summary, :content, :news_date, :status, :featured, :image
            )
            ";
            
            $stmt = $connection->prepare($sql);
            
            $result = $stmt->execute([
                ':title' => $data['title'],
                ':summary' => $data['summary'] ?? '',
                ':content' => $data['content'],
                ':news_date' => $data['news_date'],
                ':status' => $data['status'] ?? 'published',
                ':featured' => $data['featured'] ? 1 : 0,
                ':image' => $data['image'] ?? ''
            ]);
            
            if ($result) {
                return $connection->lastInsertId();
            }
        } else if ($connection instanceof mysqli) {
            $sql = "
            INSERT INTO news (
                title, summary, content, news_date, status, featured, image
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $connection->prepare($sql);
            if ($stmt) {
                $featured = $data['featured'] ? 1 : 0;
                $stmt->bind_param("sssssss", 
                    $data['title'],
                    $data['summary'] ?? '',
                    $data['content'],
                    $data['news_date'],
                    $data['status'] ?? 'published',
                    $featured,
                    $data['image'] ?? ''
                );
                
                if ($stmt->execute()) {
                    $id = $connection->insert_id;
                    $stmt->close();
                    return $id;
                }
                $stmt->close();
            }
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Failed to add news: " . $e->getMessage());
        return false;
    }
}

/**
 * Update news in database
 */
function updateNewsInDB($id, $data) {
    $connection = getDatabaseConnection();
    if (!$connection) return false;
    
    try {
        if ($connection instanceof PDO) {
            $sql = "
            UPDATE news SET 
                title = :title,
                summary = :summary,
                content = :content,
                news_date = :news_date,
                status = :status,
                featured = :featured,
                image = :image,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :id
            ";
            
            $stmt = $connection->prepare($sql);
            
            return $stmt->execute([
                ':id' => $id,
                ':title' => $data['title'],
                ':summary' => $data['summary'] ?? '',
                ':content' => $data['content'],
                ':news_date' => $data['news_date'],
                ':status' => $data['status'] ?? 'published',
                ':featured' => $data['featured'] ? 1 : 0,
                ':image' => $data['image'] ?? ''
            ]);
        } else if ($connection instanceof mysqli) {
            $sql = "
            UPDATE news SET 
                title = ?,
                summary = ?,
                content = ?,
                news_date = ?,
                status = ?,
                featured = ?,
                image = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            ";
            
            $stmt = $connection->prepare($sql);
            if ($stmt) {
                $featured = $data['featured'] ? 1 : 0;
                $stmt->bind_param("sssssssi", 
                    $data['title'],
                    $data['summary'] ?? '',
                    $data['content'],
                    $data['news_date'],
                    $data['status'] ?? 'published',
                    $featured,
                    $data['image'] ?? '',
                    $id
                );
                
                $result = $stmt->execute();
                $stmt->close();
                return $result;
            }
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Failed to update news: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete news from database
 */
function deleteNewsFromDB($id) {
    $connection = getDatabaseConnection();
    if (!$connection) return false;
    
    try {
        if ($connection instanceof PDO) {
            $stmt = $connection->prepare("DELETE FROM news WHERE id = :id");
            return $stmt->execute([':id' => $id]);
        } else if ($connection instanceof mysqli) {
            $stmt = $connection->prepare("DELETE FROM news WHERE id = ?");
            if ($stmt) {
                $stmt->bind_param("i", $id);
                $result = $stmt->execute();
                $stmt->close();
                return $result;
            }
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Failed to delete news: " . $e->getMessage());
        return false;
    }
}

/**
 * Get news by ID from database
 */
function getNewsByIdFromDB($id) {
    $connection = getDatabaseConnection();
    if (!$connection) return null;
    
    try {
        if ($connection instanceof PDO) {
            $stmt = $connection->prepare("SELECT * FROM news WHERE id = :id");
            $stmt->execute([':id' => $id]);
            return $stmt->fetch();
        } else if ($connection instanceof mysqli) {
            $stmt = $connection->prepare("SELECT * FROM news WHERE id = ?");
            if ($stmt) {
                $stmt->bind_param("i", $id);
                $stmt->execute();
                $result = $stmt->get_result();
                $data = $result->fetch_assoc();
                $stmt->close();
                return $data;
            }
        }
        
        return null;
    } catch (Exception $e) {
        error_log("Failed to get news: " . $e->getMessage());
        return null;
    }
}

/**
 * Get featured news from database
 */
function getFeaturedNewsFromDB() {
    $connection = getDatabaseConnection();
    if (!$connection) return [];
    
    try {
        if ($connection instanceof PDO) {
            $stmt = $connection->prepare("SELECT * FROM news WHERE featured = 1 AND status = 'published' ORDER BY news_date DESC");
            $stmt->execute();
            return $stmt->fetchAll();
        } else if ($connection instanceof mysqli) {
            $result = $connection->query("SELECT * FROM news WHERE featured = 1 AND status = 'published' ORDER BY news_date DESC");
            if ($result) {
                return $result->fetch_all(MYSQLI_ASSOC);
            }
        }
    } catch (Exception $e) {
        error_log("Failed to get featured news: " . $e->getMessage());
    }
    
    return [];
}

/**
 * Get news statistics from database
 */
function getNewsStatsFromDB() {
    $connection = getDatabaseConnection();
    if (!$connection) {
        return [
            'total' => 0,
            'published' => 0,
            'draft' => 0,
            'featured' => 0,
            'this_month' => 0
        ];
    }
    
    try {
        if ($connection instanceof PDO) {
            $stmt = $connection->query("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                    SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft,
                    SUM(CASE WHEN featured = 1 THEN 1 ELSE 0 END) as featured,
                    SUM(CASE WHEN YEAR(news_date) = YEAR(CURRENT_DATE) AND MONTH(news_date) = MONTH(CURRENT_DATE) THEN 1 ELSE 0 END) as this_month
                FROM news
            ");
            
            $result = $stmt->fetch();
        } else if ($connection instanceof mysqli) {
            $result = $connection->query("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                    SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft,
                    SUM(CASE WHEN featured = 1 THEN 1 ELSE 0 END) as featured,
                    SUM(CASE WHEN YEAR(news_date) = YEAR(CURRENT_DATE) AND MONTH(news_date) = MONTH(CURRENT_DATE) THEN 1 ELSE 0 END) as this_month
                FROM news
            ");
            
            if ($result) {
                $result = $result->fetch_assoc();
            }
        }
        
        return [
            'total' => (int)$result['total'],
            'published' => (int)$result['published'],
            'draft' => (int)$result['draft'],
            'featured' => (int)$result['featured'],
            'this_month' => (int)$result['this_month']
        ];
    } catch (Exception $e) {
        error_log("Failed to get news stats: " . $e->getMessage());
        return [
            'total' => 0,
            'published' => 0,
            'draft' => 0,
            'featured' => 0,
            'this_month' => 0
        ];
    }
}

// Initialize the table when this file is included
initializeNewsTable();
?>
