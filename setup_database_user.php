<?php
// Setup database user and permissions
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Database User Setup</h1>";

// Try to connect as root first
try {
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Connected as root</p>";
    
    // Create database if it doesn't exist
    echo "<h2>Creating Database</h2>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS ngo_charity CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ Database 'ngo_charity' created/verified</p>";
    
    // Create user if it doesn't exist
    echo "<h2>Creating User</h2>";
    try {
        $pdo->exec("CREATE USER 'ngo'@'localhost' IDENTIFIED BY 'Ngo1234@#charity'");
        echo "<p style='color: green;'>✅ User 'ngo' created</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'already exists') !== false) {
            echo "<p style='color: orange;'>⚠️ User 'ngo' already exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Error creating user: " . $e->getMessage() . "</p>";
        }
    }
    
    // Grant permissions
    echo "<h2>Granting Permissions</h2>";
    $pdo->exec("GRANT ALL PRIVILEGES ON ngo_charity.* TO 'ngo'@'localhost'");
    $pdo->exec("FLUSH PRIVILEGES");
    echo "<p style='color: green;'>✅ Permissions granted to 'ngo' user</p>";
    
    // Test connection with new user
    echo "<h2>Testing New User Connection</h2>";
    try {
        $testPdo = new PDO("mysql:host=localhost;dbname=ngo_charity;charset=utf8mb4", 'ngo', 'Ngo1234@#charity');
        $testPdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p style='color: green;'>✅ Connection with 'ngo' user successful</p>";
        
        // Create activities table
        echo "<h2>Creating Activities Table</h2>";
        $sql = "
        CREATE TABLE IF NOT EXISTS activities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL COMMENT 'عنوان النشاط',
            description TEXT COMMENT 'وصف النشاط',
            content TEXT COMMENT 'محتوى النشاط التفصيلي',
            image_url VARCHAR(500) COMMENT 'رابط صورة النشاط',
            activity_date DATE COMMENT 'تاريخ النشاط',
            location VARCHAR(255) COMMENT 'مكان النشاط',
            beneficiaries_count INT DEFAULT 0 COMMENT 'عدد المستفيدين',
            budget DECIMAL(12,2) DEFAULT 0.00 COMMENT 'ميزانية النشاط',
            status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT 'حالة النشاط',
            category VARCHAR(100) DEFAULT 'general' COMMENT 'فئة النشاط',
            featured BOOLEAN DEFAULT FALSE COMMENT 'نشاط مميز',
            image VARCHAR(255) DEFAULT '' COMMENT 'مسار الصورة',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_by VARCHAR(100) DEFAULT 'admin'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $testPdo->exec($sql);
        echo "<p style='color: green;'>✅ Activities table created/verified</p>";
        
        // Insert sample data if table is empty
        $stmt = $testPdo->query("SELECT COUNT(*) as count FROM activities");
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            echo "<h2>Inserting Sample Data</h2>";
            $sampleData = [
                [
                    'title' => 'توزيع المساعدات الغذائية',
                    'description' => 'توزيع سلل غذائية على العائلات المحتاجة',
                    'content' => 'تم تنظيم حملة لتوزيع السلل الغذائية على 100 عائلة محتاجة في منطقة حي الكفاءات.',
                    'activity_date' => '2024-12-15',
                    'location' => 'حي الكفاءات الثانية - الموصل',
                    'beneficiaries_count' => 100,
                    'budget' => 2500000.00,
                    'status' => 'completed',
                    'category' => 'food',
                    'featured' => 1
                ],
                [
                    'title' => 'حملة التبرع بالدم',
                    'description' => 'تنظيم حملة للتبرع بالدم لصالح مستشفيات المدينة',
                    'content' => 'نظمت الرابطة حملة للتبرع بالدم بالتعاون مع بنك الدم المركزي في الموصل.',
                    'activity_date' => '2024-12-10',
                    'location' => 'مركز الرابطة - الموصل',
                    'beneficiaries_count' => 80,
                    'budget' => 500000.00,
                    'status' => 'completed',
                    'category' => 'health',
                    'featured' => 1
                ]
            ];
            
            $insertSql = "INSERT INTO activities (title, description, content, activity_date, location, beneficiaries_count, budget, status, category, featured) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $testPdo->prepare($insertSql);
            
            foreach ($sampleData as $data) {
                $stmt->execute([
                    $data['title'],
                    $data['description'],
                    $data['content'],
                    $data['activity_date'],
                    $data['location'],
                    $data['beneficiaries_count'],
                    $data['budget'],
                    $data['status'],
                    $data['category'],
                    $data['featured']
                ]);
            }
            
            echo "<p style='color: green;'>✅ Sample data inserted</p>";
        } else {
            echo "<p style='color: green;'>✅ Activities table already has data (" . $result['count'] . " records)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error with 'ngo' user connection: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error connecting as root: " . $e->getMessage() . "</p>";
    echo "<p>Make sure MySQL is running and root access is available.</p>";
}

echo "<h2>Setup Complete</h2>";
echo "<p><a href='debug_activities.php'>Test Activities Loading</a></p>";
echo "<p><a href='activities.php'>View Activities Page</a></p>";
?>
