<?php
// Test script to check activities loading
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Testing Activities Loading</h1>";

// Test 1: Check if activities_manager.php can be included
echo "<h2>1. Testing activities_manager.php inclusion</h2>";
try {
    require_once __DIR__ . '/admin/activities_manager.php';
    echo "<p style='color: green;'>✅ activities_manager.php loaded successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error loading activities_manager.php: " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Check database connection
echo "<h2>2. Testing database connection</h2>";
$connection = getDatabaseConnection();
if ($connection) {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    echo "<p>Connection type: " . get_class($connection) . "</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Database connection failed, will use JSON fallback</p>";
}

// Test 3: Check JSON file
echo "<h2>3. Testing JSON file access</h2>";
$jsonFile = __DIR__ . '/database/data/activities.json';
if (file_exists($jsonFile)) {
    echo "<p style='color: green;'>✅ JSON file exists: $jsonFile</p>";
    $jsonContent = file_get_contents($jsonFile);
    $jsonData = json_decode($jsonContent, true);
    if ($jsonData) {
        echo "<p style='color: green;'>✅ JSON file is valid, contains " . count($jsonData) . " activities</p>";
    } else {
        echo "<p style='color: red;'>❌ JSON file is invalid</p>";
    }
} else {
    echo "<p style='color: red;'>❌ JSON file not found: $jsonFile</p>";
}

// Test 4: Test getActivities function
echo "<h2>4. Testing getActivities() function</h2>";
try {
    $activities = getActivities();
    echo "<p style='color: green;'>✅ getActivities() executed successfully</p>";
    echo "<p>Number of activities returned: " . count($activities) . "</p>";
    
    if (!empty($activities)) {
        echo "<h3>Sample activity:</h3>";
        echo "<pre>" . print_r($activities[0], true) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error in getActivities(): " . $e->getMessage() . "</p>";
}

// Test 5: Test getFeaturedActivities function
echo "<h2>5. Testing getFeaturedActivities() function</h2>";
try {
    $featuredActivities = getFeaturedActivities();
    echo "<p style='color: green;'>✅ getFeaturedActivities() executed successfully</p>";
    echo "<p>Number of featured activities: " . count($featuredActivities) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error in getFeaturedActivities(): " . $e->getMessage() . "</p>";
}

// Test 6: Test getActivitiesStats function
echo "<h2>6. Testing getActivitiesStats() function</h2>";
try {
    $stats = getActivitiesStats();
    echo "<p style='color: green;'>✅ getActivitiesStats() executed successfully</p>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error in getActivitiesStats(): " . $e->getMessage() . "</p>";
}

echo "<h2>Test Complete</h2>";
?>
