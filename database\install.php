<?php
/**
 * سكريبت تثبيت قاعدة البيانات
 * تشغيل هذا الملف لإنشاء قاعدة البيانات والجداول
 */

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تثبيت قاعدة البيانات</title>
    <style>
        body { font-family: 'Cairo', Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🛠️ تثبيت قاعدة بيانات المنظمة الخيرية</h1>";

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'ngo_charity';

try {
    echo "<h2>📋 مراحل التثبيت</h2>";
    
    // الاتصال بخادم MySQL
    echo "<div class='info'>🔌 الاتصال بخادم MySQL...</div>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✅ تم الاتصال بخادم MySQL بنجاح</div>";
    
    // إنشاء قاعدة البيانات
    echo "<div class='info'>🗄️ إنشاء قاعدة البيانات '$database'...</div>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<div class='success'>✅ تم إنشاء قاعدة البيانات '$database' بنجاح</div>";
    
    // الاتصال بقاعدة البيانات
    $pdo->exec("USE $database");
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات '$database'</div>";
    
    // قراءة ملف SQL
    echo "<div class='info'>📜 قراءة ملف SQL...</div>";
    $sql_file = __DIR__ . '/database_setup.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // تقسيم الاستعلامات
    $queries = array_filter(array_map('trim', explode(';', $sql_content)));
    
    echo "<div class='success'>✅ تم قراءة ملف SQL (" . count($queries) . " استعلام)</div>";
    
    // تنفيذ الاستعلامات
    echo "<h2>⚡ تنفيذ الاستعلامات</h2>";
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($queries as $index => $query) {
        if (empty($query) || strpos($query, '--') === 0) {
            continue;
        }
        
        try {
            // تخطي استعلامات USE و CREATE DATABASE لأنها تم تنفيذها مسبقاً
            if (stripos($query, 'USE ') === 0 || stripos($query, 'CREATE DATABASE') === 0) {
                continue;
            }
            
            $pdo->exec($query);
            $success_count++;
            
            // عرض تفاصيل الاستعلامات المهمة
            if (stripos($query, 'CREATE TABLE') === 0) {
                preg_match('/CREATE TABLE.*?`?(\w+)`?\s*\(/i', $query, $matches);
                $table_name = isset($matches[1]) ? $matches[1] : 'غير محدد';
                echo "<div class='success'>✅ تم إنشاء جدول: $table_name</div>";
            } elseif (stripos($query, 'INSERT INTO') === 0) {
                preg_match('/INSERT INTO\s+`?(\w+)`?/i', $query, $matches);
                $table_name = isset($matches[1]) ? $matches[1] : 'غير محدد';
                echo "<div class='success'>✅ تم إدراج بيانات في جدول: $table_name</div>";
            } elseif (stripos($query, 'CREATE VIEW') === 0) {
                preg_match('/CREATE VIEW\s+`?(\w+)`?/i', $query, $matches);
                $view_name = isset($matches[1]) ? $matches[1] : 'غير محدد';
                echo "<div class='success'>✅ تم إنشاء عرض: $view_name</div>";
            } elseif (stripos($query, 'CREATE INDEX') === 0) {
                preg_match('/CREATE INDEX\s+`?(\w+)`?/i', $query, $matches);
                $index_name = isset($matches[1]) ? $matches[1] : 'غير محدد';
                echo "<div class='success'>✅ تم إنشاء فهرس: $index_name</div>";
            }
            
        } catch (PDOException $e) {
            $error_count++;
            echo "<div class='error'>❌ خطأ في الاستعلام " . ($index + 1) . ": " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<h2>📊 ملخص التثبيت</h2>";
    echo "<div class='info'>";
    echo "<strong>إجمالي الاستعلامات المنفذة:</strong> " . ($success_count + $error_count) . "<br>";
    echo "<strong>الاستعلامات الناجحة:</strong> $success_count<br>";
    echo "<strong>الاستعلامات الفاشلة:</strong> $error_count<br>";
    echo "</div>";
    
    if ($error_count == 0) {
        echo "<div class='success'>";
        echo "<h2>🎉 تم تثبيت قاعدة البيانات بنجاح!</h2>";
        echo "<p>تم إنشاء جميع الجداول والبيانات التجريبية بنجاح.</p>";
        echo "</div>";
        
        // عرض معلومات الاتصال
        echo "<h2>🔗 معلومات الاتصال</h2>";
        echo "<div class='info'>";
        echo "<strong>اسم قاعدة البيانات:</strong> $database<br>";
        echo "<strong>المضيف:</strong> $host<br>";
        echo "<strong>اسم المستخدم:</strong> $username<br>";
        echo "<strong>كلمة المرور:</strong> " . (empty($password) ? 'فارغة' : '***') . "<br>";
        echo "</div>";
        
        // عرض بيانات تسجيل الدخول للوحة الإدارة
        echo "<div class='warning'>";
        echo "<h3>🔐 بيانات تسجيل الدخول للوحة الإدارة</h3>";
        echo "<strong>اسم المستخدم:</strong> admin<br>";
        echo "<strong>كلمة المرور:</strong> password<br>";
        echo "<p><small>⚠️ تأكد من تغيير كلمة المرور الافتراضية بعد أول تسجيل دخول!</small></p>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>";
        echo "<h2>⚠️ تم التثبيت مع أخطاء</h2>";
        echo "<p>بعض الاستعلامات فشلت. يرجى مراجعة الأخطاء أعلاه.</p>";
        echo "</div>";
    }
    
    // عرض الجداول المنشأة
    echo "<h2>📋 الجداول المنشأة</h2>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if ($tables) {
        echo "<div class='info'>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ خطأ في التثبيت</h2>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h3>💡 اقتراحات لحل المشكلة:</h3>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تأكد من صحة بيانات الاتصال (المضيف، اسم المستخدم، كلمة المرور)</li>";
    echo "<li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "<li>تأكد من وجود ملف database_setup.sql في نفس المجلد</li>";
    echo "</ul>";
    echo "</div>";
}

echo "
        <h2>🚀 الخطوات التالية</h2>
        <div class='info'>
            <ol>
                <li>تحديث ملف <code>database/db_config.php</code> ببيانات الاتصال الصحيحة إذا لزم الأمر</li>
                <li>اختبار الاتصال بقاعدة البيانات من موقعك</li>
                <li>تسجيل الدخول لوحة الإدارة وتغيير كلمة المرور الافتراضية</li>
                <li>إضافة النشاطات والبيانات الحقيقية</li>
            </ol>
        </div>
        
        <div style='text-align: center; margin-top: 30px;'>
            <a href='../index.php' class='btn'>🏠 العودة للموقع الرئيسي</a>
            <a href='../admin/' class='btn'>⚙️ لوحة الإدارة</a>
        </div>
    </div>
</body>
</html>";
?>
