<?php
require_once 'auth.php';

echo "<h2>إعادة تعيين كلمة مرور المدير</h2>";

// Reset admin password to "admin123"
$newPassword = 'admin123';
$hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

// Get current users
$usersFile = __DIR__ . '/data/admin_users.json';
$users = [];

if (file_exists($usersFile)) {
    $content = file_get_contents($usersFile);
    $users = json_decode($content, true) ?: [];
}

// Find admin user and update password
$updated = false;
foreach ($users as &$user) {
    if ($user['username'] === 'admin') {
        $user['password'] = $hashedPassword;
        $updated = true;
        echo "<p style='color: green;'>✅ تم تحديث كلمة مرور المدير</p>";
        break;
    }
}

if ($updated) {
    // Save updated users
    file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "<p><strong>بيانات الدخول الجديدة:</strong></p>";
    echo "<ul>";
    echo "<li>اسم المستخدم: <code>admin</code></li>";
    echo "<li>كلمة المرور: <code>admin123</code></li>";
    echo "</ul>";
    
    // Test the new password
    echo "<h3>اختبار كلمة المرور الجديدة:</h3>";
    $testUser = authenticateAdmin('admin', 'admin123');
    if ($testUser) {
        echo "<p style='color: green;'>✅ تم اختبار تسجيل الدخول بنجاح</p>";
        echo "<p>يمكنك الآن تسجيل الدخول باستخدام البيانات أعلاه</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في اختبار تسجيل الدخول</p>";
    }
} else {
    echo "<p style='color: red;'>❌ لم يتم العثور على المستخدم admin</p>";
}

echo "<hr>";
echo "<p><a href='login.php'>🔐 انتقال لصفحة تسجيل الدخول</a></p>";
echo "<p><a href='test_db.php'>🔧 اختبار النظام</a></p>";
?>
