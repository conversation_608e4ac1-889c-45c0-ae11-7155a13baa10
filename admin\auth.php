<?php
/**
 * Authentication system with database and file fallback
 */

function getAdminUsersFile() {
    return __DIR__ . '/data/admin_users.json';
}

function initializeAdminUsers() {
    $dataDir = __DIR__ . '/data';
    $usersFile = getAdminUsersFile();
    
    // Create data directory if it doesn't exist
    if (!is_dir($dataDir)) {
        mkdir($dataDir, 0755, true);
    }
    
    // Create default admin users file if it doesn't exist
    if (!file_exists($usersFile)) {
        $defaultUsers = [
            [
                'id' => 1,
                'username' => 'admin',
                'password' => 'admin123',
                'full_name' => 'مدير النظام',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'last_login' => null
            ]
        ];
        
        file_put_contents($usersFile, json_encode($defaultUsers, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
    }
}

function getAdminUsers() {
    $usersFile = getAdminUsersFile();
    
    if (!file_exists($usersFile)) {
        initializeAdminUsers();
    }
    
    $data = file_get_contents($usersFile);
    return json_decode($data, true) ?: [];
}

function saveAdminUsers($users) {
    $usersFile = getAdminUsersFile();
    return file_put_contents($usersFile, json_encode($users, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
}

function authenticateAdminFile($username, $password) {
    $users = getAdminUsers();
    
    foreach ($users as $user) {
        if ($user['username'] === $username && $user['status'] === 'active') {
            // Check password (plain text comparison)
            if ($user['password'] === $password) {
                return $user;
            }
        }
    }
    
    return false;
}

function updateLastLogin($userId) {
    $users = getAdminUsers();
    
    foreach ($users as &$user) {
        if ($user['id'] == $userId) {
            $user['last_login'] = date('Y-m-d H:i:s');
            break;
        }
    }
    
    saveAdminUsers($users);
}

function getAdminDatabaseConnection() {
    try {
        // Try PDO first
        if (class_exists('PDO') && extension_loaded('pdo_mysql')) {
            $pdo = new PDO("mysql:host=localhost;dbname=ngo_charity;charset=utf8mb4", 'root', '');
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            return ['type' => 'pdo', 'connection' => $pdo];
        }
        // Try MySQLi
        elseif (class_exists('mysqli')) {
            $mysqli = new mysqli('localhost', 'root', '', 'ngo_charity');
            if ($mysqli->connect_error) {
                throw new Exception("Connection failed: " . $mysqli->connect_error);
            }
            $mysqli->set_charset("utf8mb4");
            return ['type' => 'mysqli', 'connection' => $mysqli];
        }
        else {
            return false;
        }
    } catch (Exception $e) {
        return false;
    }
}

function authenticateAdmin($username, $password) {
    // Try database first
    $db = getAdminDatabaseConnection();
    
    if ($db) {
        try {
            if ($db['type'] === 'pdo') {
                $stmt = $db['connection']->prepare("SELECT id, username, password, full_name, email FROM admin_users WHERE username = ? AND status = 'active'");
                $stmt->execute([$username]);
                $user = $stmt->fetch();
            } else { // mysqli
                $stmt = $db['connection']->prepare("SELECT id, username, password, full_name, email FROM admin_users WHERE username = ? AND status = 'active'");
                $stmt->bind_param('s', $username);
                $stmt->execute();
                $result = $stmt->get_result();
                $user = $result->fetch_assoc();
            }
            
            if ($user) {
                // Check password (plain text comparison)
                if ($user['password'] === $password) {
                    return $user;
                }
            }
            
            return false;
            
        } catch (Exception $e) {
            // Fall back to file system
            return authenticateAdminFile($username, $password);
        }
    } else {
        // Use file system
        return authenticateAdminFile($username, $password);
    }
}

function createDefaultAdminUser() {
    $db = getAdminDatabaseConnection();
    
    if ($db) {
        try {
            // Check if admin_users table exists
            if ($db['type'] === 'pdo') {
                $stmt = $db['connection']->query("SHOW TABLES LIKE 'admin_users'");
                $tableExists = $stmt->rowCount() > 0;
            } else { // mysqli
                $result = $db['connection']->query("SHOW TABLES LIKE 'admin_users'");
                $tableExists = $result->num_rows > 0;
            }
            
            if (!$tableExists) {
                // Create admin_users table
                $createTable = "
                    CREATE TABLE admin_users (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        username VARCHAR(100) UNIQUE NOT NULL,
                        password VARCHAR(255) NOT NULL,
                        full_name VARCHAR(255) NOT NULL,
                        email VARCHAR(255) UNIQUE NOT NULL,
                        role ENUM('admin', 'manager', 'editor') DEFAULT 'admin',
                        status ENUM('active', 'inactive') DEFAULT 'active',
                        last_login TIMESTAMP NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                
                if ($db['type'] === 'pdo') {
                    $db['connection']->exec($createTable);
                } else {
                    $db['connection']->query($createTable);
                }
            }
            
            // Check if default admin user exists
            if ($db['type'] === 'pdo') {
                $stmt = $db['connection']->prepare("SELECT id FROM admin_users WHERE username = ?");
                $stmt->execute(['admin']);
                $userExists = $stmt->rowCount() > 0;
            } else {
                $stmt = $db['connection']->prepare("SELECT id FROM admin_users WHERE username = ?");
                $stmt->bind_param('s', $defaultUsername = 'admin');
                $stmt->execute();
                $result = $stmt->get_result();
                $userExists = $result->num_rows > 0;
            }
            
            if (!$userExists) {
                // Create default admin user
                $password = 'admin123';
                
                if ($db['type'] === 'pdo') {
                    $stmt = $db['connection']->prepare("INSERT INTO admin_users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute(['admin', $password, 'مدير النظام', '<EMAIL>', 'admin']);
                } else {
                    $stmt = $db['connection']->prepare("INSERT INTO admin_users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
                    $stmt->bind_param('sssss', 
                        $username = 'admin', 
                        $password, 
                        $fullName = 'مدير النظام', 
                        $email = '<EMAIL>', 
                        $role = 'admin'
                    );
                    $stmt->execute();
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            // Fall back to file system
            initializeAdminUsers();
            return true;
        }
    } else {
        // Use file system
        initializeAdminUsers();
        return true;
    }
}
?>
