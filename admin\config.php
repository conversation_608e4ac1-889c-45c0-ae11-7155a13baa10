<?php
// Database configuration for NGO Charity website

// Check if we're on production server or local
if (isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'localhost') === false) {
    // Production server environment
    define('DB_HOST', 'localhost');
    define('DB_USER', 'ngo');
    define('DB_PASS', 'Ngo1234@#charity');
    define('DB_NAME', 'ngo_charity');

    // Production settings
    define('ENVIRONMENT', 'production');
    define('DEBUG_MODE', false);
} else {
    // Local development environment
    define('DB_HOST', 'localhost');
    define('DB_USER', 'ngo');
    define('DB_PASS', 'Ngo1234@#charity');
    define('DB_NAME', 'ngo_charity');

    // Development settings
    define('ENVIRONMENT', 'development');
    define('DEBUG_MODE', true);
}

// Common settings
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('JSON_DATA_PATH', __DIR__ . '/../database/data/');
?>
