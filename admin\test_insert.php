<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/config.php';

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
}

$title = "نشاط تجريبي";
$description = "هذا نشاط تجريبي للتأكد من الإضافة";
$activity_date = date('Y-m-d');
$status = "pending";

$sql = "INSERT INTO activities (title, description, activity_date, status) VALUES (?, ?, ?, ?)";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ssss", $title, $description, $activity_date, $status);

if ($stmt->execute()) {
    echo "✅ تم إدخال النشاط التجريبي بنجاح";
} else {
    echo "❌ فشل في إدخال النشاط: " . $conn->error;
}

$stmt->close();
$conn->close();
?>