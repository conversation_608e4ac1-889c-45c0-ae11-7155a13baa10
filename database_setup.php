<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - رابطة أعن بإحسان</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2c5aa0;
            font-size: 2rem;
            margin: 0 0 10px 0;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid;
        }
        
        .status.success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .status.info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .progress {
            background: #e9ecef;
            border-radius: 20px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #28a745, #20c997);
            height: 100%;
            border-radius: 20px;
            transition: width 0.3s ease;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        
        .btn {
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(44, 90, 160, 0.3);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .table th, .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c5aa0;
        }
        
        .icon {
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ إعداد قاعدة البيانات</h1>
            <p>إعداد قاعدة البيانات لموقع رابطة أعن بإحسان الخيرية</p>
        </div>

<?php
$steps = [
    'الاتصال بخادم MySQL',
    'إنشاء قاعدة البيانات',
    'إنشاء الجداول',
    'إدراج البيانات التجريبية',
    'التحقق من التثبيت'
];

$currentStep = 0;
$errors = [];
$success = [];

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'ngo_db';

try {
    // Step 1: Connect to MySQL server
    echo "<div class='status info'><strong>الخطوة 1:</strong> محاولة الاتصال بخادم MySQL...</div>";
    
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $success[] = "✅ تم الاتصال بخادم MySQL بنجاح";
    $currentStep = 1;
    
    // Step 2: Create database
    echo "<div class='status info'><strong>الخطوة 2:</strong> إنشاء قاعدة البيانات...</div>";
    
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $success[] = "✅ تم إنشاء قاعدة البيانات: $dbname";
    $currentStep = 2;
    
    // Connect to the new database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Step 3: Create tables
    echo "<div class='status info'><strong>الخطوة 3:</strong> إنشاء الجداول...</div>";
    
    // Activities table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            activity_date DATE,
            image_path VARCHAR(255),
            status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // News table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS news (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            image_path VARCHAR(255),
            published BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Beneficiaries table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS beneficiaries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            family_size INT,
            address TEXT,
            phone VARCHAR(20),
            needs TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Donations table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS donations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            donor_name VARCHAR(255),
            amount DECIMAL(10,2),
            donation_type ENUM('money', 'goods', 'services') DEFAULT 'money',
            description TEXT,
            date_received DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Volunteers table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS volunteers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            phone VARCHAR(20),
            skills TEXT,
            availability TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Admin users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            full_name VARCHAR(255),
            role ENUM('admin', 'moderator') DEFAULT 'admin',
            status ENUM('active', 'inactive') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    $success[] = "✅ تم إنشاء جميع الجداول بنجاح";
    $currentStep = 3;
    
    // Step 4: Insert sample data
    echo "<div class='status info'><strong>الخطوة 4:</strong> إدراج البيانات التجريبية...</div>";
    
    // Insert default settings
    $pdo->exec("
        INSERT IGNORE INTO settings (setting_key, setting_value) VALUES 
        ('organization_name', 'رابطة أعن بإحسان الخيرية'),
        ('email', '<EMAIL>'),
        ('address', 'الموصل - العراق - حي الكفاءات الثانية'),
        ('phone', ''),
        ('facebook', ''),
        ('instagram', ''),
        ('twitter', '')
    ");
    
    // Insert default admin user
    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->prepare("
        INSERT IGNORE INTO admin_users (username, password, email, full_name) VALUES 
        (?, ?, '<EMAIL>', 'المدير العام')
    ")->execute(['admin', $hashedPassword]);
    
    // Insert sample activities
    $pdo->exec("
        INSERT IGNORE INTO activities (id, title, description, activity_date, status) VALUES 
        (1, 'توزيع مساعدات شتوية', 'توزيع بطانيات ومدافئ للعوائل المحتاجة في الأحياء الفقيرة', '2024-01-15', 'completed'),
        (2, 'حملة توعية صحية', 'حملة توعية حول النظافة الشخصية والوقاية من الأمراض', '2024-02-10', 'pending'),
        (3, 'إفطار رمضاني', 'توزيع وجبات إفطار مجانية للصائمين', '2024-03-20', 'pending')
    ");
    
    // Insert sample beneficiaries
    $pdo->exec("
        INSERT IGNORE INTO beneficiaries (id, name, family_size, address, needs) VALUES 
        (1, 'عائلة أحمد محمد', 6, 'حي الشفاء', 'مساعدات غذائية ومالية'),
        (2, 'عائلة فاطمة علي', 4, 'حي النور', 'مساعدات طبية'),
        (3, 'عائلة سعد حسن', 8, 'حي السلام', 'مساعدات تعليمية وغذائية')
    ");
    
    // Insert sample donations
    $pdo->exec("
        INSERT IGNORE INTO donations (id, donor_name, amount, donation_type, description, date_received) VALUES 
        (1, 'محسن مجهول', 500000, 'money', 'تبرع نقدي للعوائل المحتاجة', '2024-01-10'),
        (2, 'تاجر محلي', 200000, 'goods', 'مواد غذائية متنوعة', '2024-01-15'),
        (3, 'طبيب متطوع', 0, 'services', 'خدمات طبية مجانية', '2024-01-20')
    ");
    
    // Insert sample news
    $pdo->exec("
        INSERT IGNORE INTO news (id, title, content) VALUES 
        (1, 'بدء حملة مساعدات الشتاء', 'تعلن رابطة أعن بإحسان الخيرية عن بدء حملة توزيع المساعدات الشتوية للعوائل المتعففة في مدينة الموصل'),
        (2, 'شكر وامتنان', 'نتقدم بجزيل الشكر والامتنان لجميع المتبرعين والداعمين لأنشطة الرابطة')
    ");
    
    $success[] = "✅ تم إدراج البيانات التجريبية بنجاح";
    $currentStep = 4;
    
    // Step 5: Verify installation
    echo "<div class='status info'><strong>الخطوة 5:</strong> التحقق من التثبيت...</div>";
    
    // Get table counts
    $tables = ['activities', 'news', 'beneficiaries', 'donations', 'volunteers', 'settings', 'admin_users'];
    $tableCounts = [];
    
    foreach ($tables as $table) {
        $result = $pdo->query("SELECT COUNT(*) FROM $table");
        $tableCounts[$table] = $result->fetchColumn();
    }
    
    $success[] = "✅ تم التحقق من جميع الجداول بنجاح";
    $currentStep = 5;
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}

// Display progress
$progressPercent = ($currentStep / count($steps)) * 100;
?>

        <div class="progress">
            <div class="progress-bar" style="width: <?php echo $progressPercent; ?>%">
                <?php echo round($progressPercent); ?>%
            </div>
        </div>

        <?php if (!empty($success)): ?>
            <div class="status success">
                <h3>✅ العمليات المكتملة:</h3>
                <?php foreach ($success as $msg): ?>
                    <p><?php echo $msg; ?></p>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="status error">
                <h3>❌ الأخطاء:</h3>
                <?php foreach ($errors as $error): ?>
                    <p><?php echo $error; ?></p>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if ($currentStep == 5 && empty($errors)): ?>
            <div class="status success">
                <h3>🎉 تم إعداد قاعدة البيانات بنجاح!</h3>
                <p>يمكنك الآن استخدام لوحة التحكم بجميع ميزاتها.</p>
            </div>

            <h3>📊 ملخص قاعدة البيانات:</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم الجدول</th>
                        <th>عدد السجلات</th>
                        <th>الوصف</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>activities</td>
                        <td><?php echo $tableCounts['activities']; ?></td>
                        <td>النشاطات والفعاليات</td>
                    </tr>
                    <tr>
                        <td>news</td>
                        <td><?php echo $tableCounts['news']; ?></td>
                        <td>الأخبار والإعلانات</td>
                    </tr>
                    <tr>
                        <td>beneficiaries</td>
                        <td><?php echo $tableCounts['beneficiaries']; ?></td>
                        <td>المستفيدين</td>
                    </tr>
                    <tr>
                        <td>donations</td>
                        <td><?php echo $tableCounts['donations']; ?></td>
                        <td>التبرعات</td>
                    </tr>
                    <tr>
                        <td>volunteers</td>
                        <td><?php echo $tableCounts['volunteers']; ?></td>
                        <td>المتطوعين</td>
                    </tr>
                    <tr>
                        <td>settings</td>
                        <td><?php echo $tableCounts['settings']; ?></td>
                        <td>إعدادات النظام</td>
                    </tr>
                    <tr>
                        <td>admin_users</td>
                        <td><?php echo $tableCounts['admin_users']; ?></td>
                        <td>مستخدمي الإدارة</td>
                    </tr>
                </tbody>
            </table>

            <div style="text-align: center; margin: 30px 0;">
                <a href="admin/login.php" class="btn">🚀 الدخول إلى لوحة التحكم</a>
                <a href="index.php" class="btn" style="background: linear-gradient(135deg, #28a745, #20c997);">🏠 العودة للموقع الرئيسي</a>
            </div>

            <div class="status info">
                <h4>🔑 بيانات تسجيل الدخول:</h4>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div style="text-align: center; margin: 30px 0;">
                <button onclick="location.reload()" class="btn">🔄 إعادة المحاولة</button>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
