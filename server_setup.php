<?php
/**
 * سكريپت إعداد الخادم للموقع
 * يجب تشغيله مرة واحدة بعد رفع الملفات
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>إعداد الخادم لموقع رابطة أعن بإحسان</h1>";

// تحميل الإعدادات
require_once 'admin/config.php';

echo "<h2>1. فحص البيئة</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Environment:</strong> " . (defined('ENVIRONMENT') ? ENVIRONMENT : 'غير محدد') . "</p>";
echo "<p><strong>Server:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد') . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

// فحص المجلدات المطلوبة
echo "<h2>2. فحص وإنشاء المجلدات</h2>";
$directories = [
    'uploads',
    'uploads/activities',
    'uploads/news',
    'database',
    'database/data'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p style='color: green;'>✅ تم إنشاء المجلد: $dir</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء المجلد: $dir</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ المجلد موجود: $dir</p>";
    }
    
    // فحص صلاحيات الكتابة
    if (is_writable($dir)) {
        echo "<p style='color: green;'>✅ المجلد $dir قابل للكتابة</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ المجلد $dir غير قابل للكتابة - قد تحتاج لتغيير الصلاحيات</p>";
        echo "<p>تشغيل الأمر: <code>chmod 755 $dir</code></p>";
    }
}

// اختبار الاتصال بقاعدة البيانات
echo "<h2>3. اختبار قاعدة البيانات</h2>";
echo "<p><strong>Host:</strong> " . DB_HOST . "</p>";
echo "<p><strong>User:</strong> " . DB_USER . "</p>";
echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";

try {
    // اختبار الاتصال
    $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ الاتصال بخادم MySQL نجح</p>";
    
    // فحص قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ قاعدة البيانات موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ قاعدة البيانات غير موجودة</p>";
        echo "<p>قم بتشغيل الأوامر التالية في MySQL:</p>";
        echo "<pre>";
        echo "CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
        echo "CREATE USER '" . DB_USER . "'@'localhost' IDENTIFIED BY '" . DB_PASS . "';\n";
        echo "GRANT ALL PRIVILEGES ON " . DB_NAME . ".* TO '" . DB_USER . "'@'localhost';\n";
        echo "FLUSH PRIVILEGES;";
        echo "</pre>";
        exit;
    }
    
    // الاتصال بقاعدة البيانات المحددة
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // إنشاء الجداول
    echo "<h2>4. إنشاء الجداول</h2>";
    
    // جدول النشاطات
    $activitiesTable = "
    CREATE TABLE IF NOT EXISTS activities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL COMMENT 'عنوان النشاط',
        description TEXT COMMENT 'وصف النشاط',
        content TEXT COMMENT 'محتوى النشاط التفصيلي',
        image_url VARCHAR(500) COMMENT 'رابط صورة النشاط',
        activity_date DATE COMMENT 'تاريخ النشاط',
        location VARCHAR(255) COMMENT 'مكان النشاط',
        beneficiaries_count INT DEFAULT 0 COMMENT 'عدد المستفيدين',
        budget DECIMAL(12,2) DEFAULT 0.00 COMMENT 'ميزانية النشاط',
        status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT 'حالة النشاط',
        category VARCHAR(100) DEFAULT 'general' COMMENT 'فئة النشاط',
        featured BOOLEAN DEFAULT FALSE COMMENT 'نشاط مميز',
        image VARCHAR(255) DEFAULT '' COMMENT 'مسار الصورة',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by VARCHAR(100) DEFAULT 'admin'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($activitiesTable);
    echo "<p style='color: green;'>✅ تم إنشاء جدول activities</p>";
    
    // جدول المستخدمين الإداريين
    $adminUsersTable = "
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        role ENUM('admin', 'manager', 'editor') DEFAULT 'admin',
        status ENUM('active', 'inactive') DEFAULT 'active',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($adminUsersTable);
    echo "<p style='color: green;'>✅ تم إنشاء جدول admin_users</p>";
    
    // إنشاء مستخدم إداري افتراضي
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        $stmt = $pdo->prepare("INSERT INTO admin_users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['admin', 'admin123', 'مدير النظام', '<EMAIL>', 'admin']);
        echo "<p style='color: green;'>✅ تم إنشاء المستخدم الإداري</p>";
        echo "<p><strong>اسم المستخدم:</strong> admin</p>";
        echo "<p><strong>كلمة المرور:</strong> admin123</p>";
    }
    
    // إدراج بيانات تجريبية إذا لم تكن موجودة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM activities");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        echo "<h2>5. إدراج البيانات التجريبية</h2>";
        
        // محاولة قراءة من ملف JSON
        $jsonFile = 'database/data/activities.json';
        if (file_exists($jsonFile)) {
            $jsonContent = file_get_contents($jsonFile);
            $activities = json_decode($jsonContent, true);
            
            if ($activities && is_array($activities)) {
                $insertSql = "INSERT INTO activities (title, description, content, activity_date, location, beneficiaries_count, budget, status, category, featured, image) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $pdo->prepare($insertSql);
                
                $inserted = 0;
                foreach ($activities as $activity) {
                    try {
                        $stmt->execute([
                            $activity['title'],
                            $activity['description'],
                            $activity['content'] ?? $activity['description'],
                            $activity['activity_date'],
                            $activity['location'] ?? '',
                            (int)($activity['beneficiaries_count'] ?? 0),
                            (float)($activity['budget'] ?? 0),
                            $activity['status'] ?? 'pending',
                            $activity['category'] ?? 'general',
                            $activity['featured'] ? 1 : 0,
                            $activity['image'] ?? ''
                        ]);
                        $inserted++;
                    } catch (Exception $e) {
                        echo "<p style='color: orange;'>⚠️ تخطي: " . $activity['title'] . "</p>";
                    }
                }
                echo "<p style='color: green;'>✅ تم إدراج $inserted نشاط من JSON</p>";
            }
        } else {
            // بيانات تجريبية أساسية
            $sampleActivities = [
                ['توزيع المساعدات الغذائية', 'توزيع سلل غذائية على العائلات المحتاجة', '2024-12-15', 'حي الكفاءات الثانية - الموصل', 100, 2500000, 'completed', 'food', 1],
                ['حملة التبرع بالدم', 'تنظيم حملة للتبرع بالدم لصالح مستشفيات المدينة', '2024-12-10', 'مركز الرابطة - الموصل', 80, 500000, 'completed', 'health', 1]
            ];
            
            $stmt = $pdo->prepare("INSERT INTO activities (title, description, activity_date, location, beneficiaries_count, budget, status, category, featured) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            
            foreach ($sampleActivities as $activity) {
                $stmt->execute($activity);
            }
            echo "<p style='color: green;'>✅ تم إدراج البيانات التجريبية الأساسية</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ قاعدة البيانات تحتوي على " . $result['count'] . " نشاط</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>6. اختبار الوظائف</h2>";
try {
    require_once 'admin/activities_manager.php';
    $activities = getActivities();
    echo "<p style='color: green;'>✅ تم تحميل " . count($activities) . " نشاط بنجاح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل النشاطات: " . $e->getMessage() . "</p>";
}

echo "<h2>✅ تم إعداد الخادم بنجاح!</h2>";
echo "<p><a href='activities.php'>اختبار صفحة النشاطات</a></p>";
echo "<p><a href='admin/login.php'>دخول لوحة التحكم</a></p>";
echo "<p style='color: red;'><strong>مهم:</strong> احذف هذا الملف بعد الانتهاء!</p>";
?>
