<?php
/**
 * سكريبت تشخيص مشاكل الاستضافة
 * يجب حذف هذا الملف بعد حل المشاكل لأسباب أمنية
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>تشخيص مشاكل الاستضافة</h1>";
echo "<p style='color: red;'><strong>تحذير:</strong> احذف هذا الملف بعد حل المشاكل!</p>";

// معلومات الخادم
echo "<h2>1. معلومات الخادم</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Host:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";

// فحص الملفات المطلوبة
echo "<h2>2. فحص الملفات المطلوبة</h2>";
$requiredFiles = [
    'admin/config.php',
    'admin/db_activities.php',
    'admin/activities_manager.php',
    'database/data/activities.json'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $file غير موجود</p>";
    }
}

// فحص صلاحيات الملفات
echo "<h2>3. فحص صلاحيات الملفات</h2>";
$directories = [
    'uploads',
    'uploads/activities',
    'database',
    'database/data'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir) ? 'قابل للكتابة' : 'غير قابل للكتابة';
        echo "<p>📁 $dir - صلاحيات: $perms - $writable</p>";
    } else {
        echo "<p style='color: red;'>❌ المجلد $dir غير موجود</p>";
    }
}

// فحص إعدادات قاعدة البيانات
echo "<h2>4. فحص إعدادات قاعدة البيانات</h2>";
if (file_exists('admin/config.php')) {
    require_once 'admin/config.php';
    echo "<p><strong>DB_HOST:</strong> " . (defined('DB_HOST') ? DB_HOST : 'غير محدد') . "</p>";
    echo "<p><strong>DB_USER:</strong> " . (defined('DB_USER') ? DB_USER : 'غير محدد') . "</p>";
    echo "<p><strong>DB_NAME:</strong> " . (defined('DB_NAME') ? DB_NAME : 'غير محدد') . "</p>";
    echo "<p><strong>DB_PASS:</strong> " . (defined('DB_PASS') ? (strlen(DB_PASS) > 0 ? 'محدد (' . strlen(DB_PASS) . ' أحرف)' : 'فارغ') : 'غير محدد') . "</p>";
} else {
    echo "<p style='color: red;'>❌ ملف config.php غير موجود</p>";
}

// اختبار الاتصال بقاعدة البيانات
echo "<h2>5. اختبار الاتصال بقاعدة البيانات</h2>";
if (defined('DB_HOST') && defined('DB_USER') && defined('DB_NAME')) {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS);
        echo "<p style='color: green;'>✅ الاتصال بخادم MySQL نجح</p>";
        
        // فحص وجود قاعدة البيانات
        $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ قاعدة البيانات " . DB_NAME . " موجودة</p>";
            
            // الاتصال بقاعدة البيانات المحددة
            $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
            echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
            
            // فحص جدول activities
            $stmt = $pdo->query("SHOW TABLES LIKE 'activities'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ جدول activities موجود</p>";
                
                // فحص عدد السجلات
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM activities");
                $result = $stmt->fetch();
                echo "<p>عدد النشاطات في قاعدة البيانات: " . $result['count'] . "</p>";
            } else {
                echo "<p style='color: red;'>❌ جدول activities غير موجود</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ قاعدة البيانات " . DB_NAME . " غير موجودة</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ إعدادات قاعدة البيانات غير مكتملة</p>";
}

// فحص ملف JSON
echo "<h2>6. فحص ملف JSON</h2>";
$jsonFile = 'database/data/activities.json';
if (file_exists($jsonFile)) {
    echo "<p style='color: green;'>✅ ملف JSON موجود</p>";
    $content = file_get_contents($jsonFile);
    $data = json_decode($content, true);
    if ($data !== null) {
        echo "<p style='color: green;'>✅ ملف JSON صالح</p>";
        echo "<p>عدد النشاطات في JSON: " . count($data) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ ملف JSON تالف</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف JSON غير موجود</p>";
}

// اختبار دوال النشاطات
echo "<h2>7. اختبار دوال النشاطات</h2>";
try {
    if (file_exists('admin/activities_manager.php')) {
        require_once 'admin/activities_manager.php';
        echo "<p style='color: green;'>✅ تم تحميل activities_manager.php</p>";
        
        $activities = getActivities();
        echo "<p>عدد النشاطات المسترجعة: " . count($activities) . "</p>";
        
        $stats = getActivitiesStats();
        echo "<p>إحصائيات النشاطات: " . json_encode($stats) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ ملف activities_manager.php غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل دوال النشاطات: " . $e->getMessage() . "</p>";
}

// معلومات PHP
echo "<h2>8. إعدادات PHP المهمة</h2>";
echo "<p><strong>upload_max_filesize:</strong> " . ini_get('upload_max_filesize') . "</p>";
echo "<p><strong>post_max_size:</strong> " . ini_get('post_max_size') . "</p>";
echo "<p><strong>max_execution_time:</strong> " . ini_get('max_execution_time') . "</p>";
echo "<p><strong>memory_limit:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>file_uploads:</strong> " . (ini_get('file_uploads') ? 'مفعل' : 'معطل') . "</p>";

echo "<h2>انتهى التشخيص</h2>";
echo "<p style='color: red;'><strong>مهم:</strong> احذف هذا الملف بعد حل المشاكل!</p>";
?>
