<?php
/**
 * Database Activities Management Functions
 */

require_once 'auth.php';
require_once __DIR__ . '/config.php';

/**
 * Get database connection
 */
function getDatabaseConnection() {
    static $connection = null;

    if ($connection === null) {
        // Try with configured credentials first
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];

            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
            $connection = $pdo;
            return $connection;
        } catch (Exception $e) {
            // Try with root credentials as fallback
            try {
                $pdo = new PDO("mysql:host=localhost;dbname=ngo_charity;charset=utf8mb4", 'root', '');
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
                $connection = $pdo;
                return $connection;
            } catch (Exception $e2) {
                // PDO failed, try MySQLi with configured credentials
                try {
                    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
                    if ($mysqli->connect_error) {
                        throw new Exception("Connection failed: " . $mysqli->connect_error);
                    }
                    $mysqli->set_charset("utf8mb4");
                    $connection = $mysqli;
                    return $connection;
                } catch (Exception $e3) {
                    // Try MySQLi with root as final fallback
                    try {
                        $mysqli = new mysqli('localhost', 'root', '', 'ngo_charity');
                        if ($mysqli->connect_error) {
                            throw new Exception("Connection failed: " . $mysqli->connect_error);
                        }
                        $mysqli->set_charset("utf8mb4");
                        $connection = $mysqli;
                        return $connection;
                    } catch (Exception $e4) {
                        error_log("All database connection attempts failed: " . $e4->getMessage());
                        return null;
                    }
                }
            }
        }
    }

    return $connection;
}

/**
 * Initialize activities table with proper schema
 */
function initializeActivitiesTable() {
    $connection = getDatabaseConnection();
    if (!$connection) return false;
    
    try {
        if ($connection instanceof PDO) {
            $sql = "
            CREATE TABLE IF NOT EXISTS activities (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL COMMENT 'عنوان النشاط',
                description TEXT COMMENT 'وصف النشاط',
                content TEXT COMMENT 'محتوى النشاط التفصيلي',
                image_url VARCHAR(500) COMMENT 'رابط صورة النشاط',
                activity_date DATE COMMENT 'تاريخ النشاط',
                location VARCHAR(255) COMMENT 'مكان النشاط',
                beneficiaries_count INT DEFAULT 0 COMMENT 'عدد المستفيدين',
                budget DECIMAL(12,2) DEFAULT 0.00 COMMENT 'ميزانية النشاط',
                status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT 'حالة النشاط',
                category VARCHAR(100) DEFAULT 'general' COMMENT 'فئة النشاط',
                featured BOOLEAN DEFAULT FALSE COMMENT 'نشاط مميز',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'admin' COMMENT 'المستخدم الذي أنشأ النشاط'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $connection->exec($sql);
        } else if ($connection instanceof mysqli) {
            $sql = "
            CREATE TABLE IF NOT EXISTS activities (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL COMMENT 'عنوان النشاط',
                description TEXT COMMENT 'وصف النشاط',
                content TEXT COMMENT 'محتوى النشاط التفصيلي',
                image_url VARCHAR(500) COMMENT 'رابط صورة النشاط',
                activity_date DATE COMMENT 'تاريخ النشاط',
                location VARCHAR(255) COMMENT 'مكان النشاط',
                beneficiaries_count INT DEFAULT 0 COMMENT 'عدد المستفيدين',
                budget DECIMAL(12,2) DEFAULT 0.00 COMMENT 'ميزانية النشاط',
                status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT 'حالة النشاط',
                category VARCHAR(100) DEFAULT 'general' COMMENT 'فئة النشاط',
                featured BOOLEAN DEFAULT FALSE COMMENT 'نشاط مميز',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'admin' COMMENT 'المستخدم الذي أنشأ النشاط'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $connection->query($sql);
        }
        return true;
    } catch (Exception $e) {
        error_log("Failed to create activities table: " . $e->getMessage());
        return false;
    }
}

/**
 * Get all activities from database
 */
function getActivitiesFromDB() {
    $connection = getDatabaseConnection();
    if (!$connection) return [];
    
    try {
        if ($connection instanceof PDO) {
            $stmt = $connection->prepare("SELECT * FROM activities ORDER BY activity_date DESC, created_at DESC");
            $stmt->execute();
            return $stmt->fetchAll();
        } else if ($connection instanceof mysqli) {
            $result = $connection->query("SELECT * FROM activities ORDER BY activity_date DESC, created_at DESC");
            if ($result) {
                return $result->fetch_all(MYSQLI_ASSOC);
            }
        }
    } catch (Exception $e) {
        error_log("Failed to get activities: " . $e->getMessage());
    }
    
    return [];
}

/**
 * Add new activity to database
 */
function addActivityToDB($data) {
    $connection = getDatabaseConnection();
    if (!$connection) return false;

    // Check if activity with same title and date already exists
    try {
        if ($connection instanceof PDO) {
            $checkSql = "SELECT id FROM activities WHERE title = :title AND activity_date = :activity_date";
            $checkStmt = $connection->prepare($checkSql);
            $checkStmt->execute([
                ':title' => $data['title'],
                ':activity_date' => $data['activity_date']
            ]);
            if ($checkStmt->fetch()) {
                $_SESSION['db_error'] = "يوجد نشاط بنفس العنوان والتاريخ مسبقاً";
                return false;
            }
        } else if ($connection instanceof mysqli) {
            $checkSql = "SELECT id FROM activities WHERE title = ? AND activity_date = ?";
            $checkStmt = $connection->prepare($checkSql);
            $checkStmt->bind_param("ss", $data['title'], $data['activity_date']);
            $checkStmt->execute();
            $result = $checkStmt->get_result();
            if ($result->fetch_assoc()) {
                $_SESSION['db_error'] = "يوجد نشاط بنفس العنوان والتاريخ مسبقاً";
                return false;
            }
        }
    } catch (Exception $e) {
        error_log("Error checking duplicate activity: " . $e->getMessage());
    }

    try {
        if ($connection instanceof PDO) {
            $sql = "
            INSERT INTO activities (
                title, description, content, image_url, activity_date, location, 
                beneficiaries_count, budget, status, category, featured, created_by
            ) VALUES (
                :title, :description, :content, :image_url, :activity_date, :location,
                :beneficiaries_count, :budget, :status, :category, :featured, :created_by
            )
            ";
            
            $stmt = $connection->prepare($sql);
            
            $result = $stmt->execute([
                ':title' => $data['title'],
                ':description' => $data['description'],
                ':content' => $data['content'] ?? $data['description'],
                ':image_url' => $data['image'] ?? '',
                ':activity_date' => $data['activity_date'],
                ':location' => $data['location'] ?? '',
                ':beneficiaries_count' => (int)($data['beneficiaries_count'] ?? 0),
                ':budget' => (float)($data['budget'] ?? 0),
                ':status' => $data['status'] ?? 'pending',
                ':category' => $data['category'] ?? 'general',
                ':featured' => $data['featured'] ? 1 : 0,
                ':created_by' => 'admin'
            ]);
            
            if ($result) {
                return $connection->lastInsertId();
            }
        } else if ($connection instanceof mysqli) {
            $sql = "
            INSERT INTO activities (
                title, description, content, image_url, activity_date, location, 
                beneficiaries_count, budget, status, category, featured, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $connection->prepare($sql);
            if ($stmt) {
                $featured = $data['featured'] ? 1 : 0;
                $stmt->bind_param("sssssiidssss", 
                    $data['title'],
                    $data['description'],
                    $data['content'] ?? $data['description'],
                    $data['image'] ?? '',
                    $data['activity_date'],
                    $data['location'] ?? '',
                    (int)($data['beneficiaries_count'] ?? 0),
                    (float)($data['budget'] ?? 0),
                    $data['status'] ?? 'pending',
                    $data['category'] ?? 'general',
                    $featured,
                    'admin'
                );
                
                $result = $stmt->execute();
                if ($result) {
                    return $connection->insert_id;
                }
                $stmt->close();
            }
        }
    } catch (Exception $e) {
        error_log("Failed to add activity: " . $e->getMessage());
        // Store the error message for debugging
        $_SESSION['db_error'] = $e->getMessage();
        return false;
    }

    return false;
}

/**
 * Update activity in database
 */
function updateActivityInDB($id, $data) {
    $connection = getDatabaseConnection();
    if (!$connection) return false;
    
    try {
        if ($connection instanceof PDO) {
            $sql = "
            UPDATE activities SET 
                title = :title,
                description = :description,
                content = :content,
                image_url = :image_url,
                activity_date = :activity_date,
                location = :location,
                beneficiaries_count = :beneficiaries_count,
                budget = :budget,
                status = :status,
                category = :category,
                featured = :featured,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :id
            ";
            
            $stmt = $connection->prepare($sql);
            
            return $stmt->execute([
                ':id' => $id,
                ':title' => $data['title'],
                ':description' => $data['description'],
                ':content' => $data['content'] ?? $data['description'],
                ':image_url' => $data['image'] ?? '',
                ':activity_date' => $data['activity_date'],
                ':location' => $data['location'] ?? '',
                ':beneficiaries_count' => (int)($data['beneficiaries_count'] ?? 0),
                ':budget' => (float)($data['budget'] ?? 0),
                ':status' => $data['status'] ?? 'pending',
                ':category' => $data['category'] ?? 'general',
                ':featured' => $data['featured'] ? 1 : 0
            ]);
        } else if ($connection instanceof mysqli) {
            $sql = "
            UPDATE activities SET 
                title = ?,
                description = ?,
                content = ?,
                image_url = ?,
                activity_date = ?,
                location = ?,
                beneficiaries_count = ?,
                budget = ?,
                status = ?,
                category = ?,
                featured = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            ";
            
            $stmt = $connection->prepare($sql);
            if ($stmt) {
                $featured = $data['featured'] ? 1 : 0;
                $stmt->bind_param("sssssiidssii", 
                    $data['title'],
                    $data['description'],
                    $data['content'] ?? $data['description'],
                    $data['image'] ?? '',
                    $data['activity_date'],
                    $data['location'] ?? '',
                    (int)($data['beneficiaries_count'] ?? 0),
                    (float)($data['budget'] ?? 0),
                    $data['status'] ?? 'pending',
                    $data['category'] ?? 'general',
                    $featured,
                    $id
                );
                
                $result = $stmt->execute();
                $stmt->close();
                return $result;
            }
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Failed to update activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete activity from database
 */
function deleteActivityFromDB($id) {
    $pdo = getDatabaseConnection();
    if (!$pdo) return false;
    
    try {
        $stmt = $pdo->prepare("DELETE FROM activities WHERE id = :id");
        return $stmt->execute([':id' => $id]);
    } catch (PDOException $e) {
        error_log("Failed to delete activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Get activity by ID from database
 */
function getActivityByIdFromDB($id) {
    $connection = getDatabaseConnection();
    if (!$connection) return null;
    
    try {
        if ($connection instanceof PDO) {
            $stmt = $connection->prepare("SELECT * FROM activities WHERE id = :id");
            $stmt->execute([':id' => $id]);
            return $stmt->fetch();
        } else if ($connection instanceof mysqli) {
            $stmt = $connection->prepare("SELECT * FROM activities WHERE id = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            $result = $stmt->get_result();
            return $result->fetch_assoc();
        }
    } catch (Exception $e) {
        error_log("Failed to get activity: " . $e->getMessage());
        return null;
    }
    
    return null;
}

/**
 * Get activities statistics from database
 */
function getActivitiesStatsFromDB() {
    $pdo = getDatabaseConnection();
    if (!$pdo) {
        return [
            'total' => 0,
            'completed' => 0,
            'pending' => 0,
            'in_progress' => 0,
            'cancelled' => 0,
            'this_month' => 0,
            'total_beneficiaries' => 0,
            'total_budget' => 0
        ];
    }
    
    try {
        // Get basic counts
        $stmt = $pdo->query("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                SUM(CASE WHEN YEAR(activity_date) = YEAR(CURRENT_DATE) AND MONTH(activity_date) = MONTH(CURRENT_DATE) THEN 1 ELSE 0 END) as this_month,
                SUM(beneficiaries_count) as total_beneficiaries,
                SUM(budget) as total_budget
            FROM activities
        ");
        
        $result = $stmt->fetch();
        
        return [
            'total' => (int)$result['total'],
            'completed' => (int)$result['completed'],
            'pending' => (int)$result['pending'],
            'in_progress' => (int)$result['in_progress'],
            'cancelled' => (int)$result['cancelled'],
            'this_month' => (int)$result['this_month'],
            'total_beneficiaries' => (int)$result['total_beneficiaries'],
            'total_budget' => (float)$result['total_budget']
        ];
    } catch (PDOException $e) {
        error_log("Failed to get activities stats: " . $e->getMessage());
        return [
            'total' => 0,
            'completed' => 0,
            'pending' => 0,
            'in_progress' => 0,
            'cancelled' => 0,
            'this_month' => 0,
            'total_beneficiaries' => 0,
            'total_budget' => 0
        ];
    }
}

// Initialize the table when this file is included
initializeActivitiesTable();
?>
