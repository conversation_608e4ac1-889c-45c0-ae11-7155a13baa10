<?php
session_start();
require_once 'admin_header.php';

// Get activities for display
$activities = getActivities();

// Calculate additional statistics
$totalActivities = count($activities);
$completedActivities = count(array_filter($activities, fn($a) => $a['status'] === 'completed'));
$pendingActivities = count(array_filter($activities, fn($a) => $a['status'] === 'pending'));
$inProgressActivities = count(array_filter($activities, fn($a) => $a['status'] === 'in_progress'));
$cancelledActivities = count(array_filter($activities, fn($a) => $a['status'] === 'cancelled'));

// Calculate budget statistics
$totalBudget = array_sum(array_column($activities, 'budget'));
$completedBudget = array_sum(array_column(array_filter($activities, fn($a) => $a['status'] === 'completed'), 'budget'));

// Calculate beneficiaries statistics
$totalBeneficiaries = array_sum(array_column($activities, 'beneficiaries_count'));
$completedBeneficiaries = array_sum(array_column(array_filter($activities, fn($a) => $a['status'] === 'completed'), 'beneficiaries_count'));

// Calculate monthly statistics
$thisMonth = date('Y-m');
$thisMonthActivities = count(array_filter($activities, fn($a) => date('Y-m', strtotime($a['activity_date'])) === $thisMonth));

// Calculate yearly statistics
$thisYear = date('Y');
$thisYearActivities = count(array_filter($activities, fn($a) => date('Y', strtotime($a['activity_date'])) === $thisYear));

// Calculate average statistics
$avgBudgetPerActivity = $totalActivities > 0 ? $totalBudget / $totalActivities : 0;
$avgBeneficiariesPerActivity = $totalActivities > 0 ? $totalBeneficiaries / $totalActivities : 0;

// Calculate monthly breakdown for the last 6 months
$monthlyStats = [];
for ($i = 5; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i month"));
    $monthlyStats[$month] = [
        'activities' => count(array_filter($activities, fn($a) => date('Y-m', strtotime($a['activity_date'])) === $month)),
        'beneficiaries' => array_sum(array_column(array_filter($activities, fn($a) => date('Y-m', strtotime($a['activity_date'])) === $month), 'beneficiaries_count')),
        'budget' => array_sum(array_column(array_filter($activities, fn($a) => date('Y-m', strtotime($a['activity_date'])) === $month), 'budget'))
    ];
}

// Calculate categories statistics
$categories = [];
foreach ($activities as $activity) {
    $cat = $activity['category'] ?? 'general';
    $categories[$cat] = ($categories[$cat] ?? 0) + 1;
}

// Calculate featured activities
$featuredActivities = count(array_filter($activities, fn($a) => $a['featured']));

// Calculate success rate
$successRate = $totalActivities > 0 ? round(($completedActivities / $totalActivities) * 100, 1) : 0;

// Get recent activities
$recentActivities = array_slice($activities, -5);

renderAdminHeader('لوحة التحكم الرئيسية');
?>

            <!-- Dashboard Content -->
            <div class="admin-section">
                <!-- Welcome Banner -->
                <div class="welcome-banner">
                    <h2><i class="fas fa-chart-line"></i> مرحباً بك في لوحة التحكم</h2>
                    <p>إدارة شاملة لأنشطة وخدمات رابطة أعن بإحسان الخيرية</p>
                </div>

                <!-- Main Statistics -->
                <div class="dashboard-overview">
                    <h3><i class="fas fa-chart-bar"></i> الإحصائيات الرئيسية</h3>
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="stat-info">
                                <h3><?php echo $totalActivities; ?></h3>
                                <p>إجمالي النشاطات</p>
                                <small>منذ بداية العمل</small>
                            </div>
                        </div>

                        <div class="stat-card success">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3><?php echo $completedActivities; ?></h3>
                                <p>النشاطات المكتملة</p>
                                <small><?php echo $totalActivities > 0 ? round(($completedActivities / $totalActivities) * 100) : 0; ?>% من الإجمالي</small>
                            </div>
                        </div>

                        <div class="stat-card warning">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <h3><?php echo $pendingActivities; ?></h3>
                                <p>النشاطات القادمة</p>
                                <small>في الانتظار</small>
                            </div>
                        </div>

                        <div class="stat-card info">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3><?php echo number_format($totalBeneficiaries); ?></h3>
                                <p>إجمالي المستفيدين</p>
                                <small>من جميع النشاطات</small>
                            </div>
                        </div>

                        <div class="stat-card secondary">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stat-info">
                                <h3><?php echo number_format($totalBudget); ?></h3>
                                <p>إجمالي الميزانيات</p>
                                <small>دينار عراقي</small>
                            </div>
                        </div>

                        <div class="stat-card danger">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-times"></i>
                            </div>
                            <div class="stat-info">
                                <h3><?php echo $thisMonthActivities; ?></h3>
                                <p>نشاطات هذا الشهر</p>
                                <small><?php echo date('F Y'); ?></small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-widgets">
                    <!-- Status Distribution -->
                    <div class="widget">
                        <h3><i class="fas fa-pie-chart"></i> توزيع حالات النشاطات</h3>
                        <div class="status-distribution">
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill completed" style="width: <?php echo $totalActivities > 0 ? ($completedActivities / $totalActivities) * 100 : 0; ?>%"></div>
                                </div>
                                <div class="status-info">
                                    <span class="status-label">مكتملة</span>
                                    <span class="status-count"><?php echo $completedActivities; ?></span>
                                </div>
                            </div>
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill in-progress" style="width: <?php echo $totalActivities > 0 ? ($inProgressActivities / $totalActivities) * 100 : 0; ?>%"></div>
                                </div>
                                <div class="status-info">
                                    <span class="status-label">جارية</span>
                                    <span class="status-count"><?php echo $inProgressActivities; ?></span>
                                </div>
                            </div>
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill pending" style="width: <?php echo $totalActivities > 0 ? ($pendingActivities / $totalActivities) * 100 : 0; ?>%"></div>
                                </div>
                                <div class="status-info">
                                    <span class="status-label">قادمة</span>
                                    <span class="status-count"><?php echo $pendingActivities; ?></span>
                                </div>
                            </div>
                            <?php if ($cancelledActivities > 0): ?>
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill cancelled" style="width: <?php echo $totalActivities > 0 ? ($cancelledActivities / $totalActivities) * 100 : 0; ?>%"></div>
                                </div>
                                <div class="status-info">
                                    <span class="status-label">ملغاة</span>
                                    <span class="status-count"><?php echo $cancelledActivities; ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Budget & Beneficiaries -->
                    <div class="widget">
                        <h3><i class="fas fa-chart-area"></i> إحصائيات الإنجاز</h3>
                        <div class="achievement-stats">
                            <div class="achievement-item">
                                <div class="achievement-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="achievement-info">
                                    <h4><?php echo number_format($completedBudget); ?> د.ع</h4>
                                    <p>الميزانيات المنفذة</p>
                                    <small>من أصل <?php echo number_format($totalBudget); ?> د.ع</small>
                                </div>
                            </div>
                            <div class="achievement-item">
                                <div class="achievement-icon">
                                    <i class="fas fa-hand-holding-heart"></i>
                                </div>
                                <div class="achievement-info">
                                    <h4><?php echo number_format($completedBeneficiaries); ?></h4>
                                    <p>المستفيدين المخدومين</p>
                                    <small>من أصل <?php echo number_format($totalBeneficiaries); ?> مستفيد</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Categories Distribution -->
                    <?php if (!empty($categories)): ?>
                    <div class="widget">
                        <h3><i class="fas fa-tags"></i> توزيع الفئات</h3>
                        <div class="categories-list">
                            <?php 
                            $categoryLabels = [
                                'general' => 'عام',
                                'food' => 'مساعدات غذائية',
                                'health' => 'صحي',
                                'education' => 'تعليمي',
                                'social' => 'اجتماعي',
                                'emergency' => 'طوارئ'
                            ];
                            foreach ($categories as $cat => $count): 
                            ?>
                                <div class="category-item">
                                    <span class="category-name"><?php echo $categoryLabels[$cat] ?? $cat; ?></span>
                                    <span class="category-count"><?php echo $count; ?></span>
                                    <div class="category-bar">
                                        <div class="category-fill" style="width: <?php echo ($count / $totalActivities) * 100; ?>%"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Advanced Statistics -->
                    <div class="widget">
                        <h3><i class="fas fa-chart-line"></i> إحصائيات متقدمة</h3>
                        <div class="advanced-stats">
                            <div class="stat-row">
                                <div class="stat-item">
                                    <div class="stat-label">معدل النجاح</div>
                                    <div class="stat-value success"><?php echo $successRate; ?>%</div>
                                    <div class="stat-description">من إجمالي النشاطات</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">النشاطات المميزة</div>
                                    <div class="stat-value warning"><?php echo $featuredActivities; ?></div>
                                    <div class="stat-description">نشاط مميز</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">نشاطات هذا العام</div>
                                    <div class="stat-value info"><?php echo $thisYearActivities; ?></div>
                                    <div class="stat-description">في <?php echo $thisYear; ?></div>
                                </div>
                            </div>
                            <div class="stat-row">
                                <div class="stat-item">
                                    <div class="stat-label">متوسط الميزانية</div>
                                    <div class="stat-value secondary"><?php echo number_format($avgBudgetPerActivity); ?></div>
                                    <div class="stat-description">د.ع لكل نشاط</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">متوسط المستفيدين</div>
                                    <div class="stat-value primary"><?php echo number_format($avgBeneficiariesPerActivity, 1); ?></div>
                                    <div class="stat-description">مستفيد لكل نشاط</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">إجمالي الفئات</div>
                                    <div class="stat-value danger"><?php echo count($categories); ?></div>
                                    <div class="stat-description">فئة نشاط</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Trends -->
                    <div class="widget">
                        <h3><i class="fas fa-calendar-alt"></i> الاتجاهات الشهرية (آخر 6 أشهر)</h3>
                        <div class="monthly-trends">
                            <?php foreach ($monthlyStats as $month => $stats): ?>
                                <div class="month-stat">
                                    <div class="month-label"><?php echo date('M Y', strtotime($month . '-01')); ?></div>
                                    <div class="month-bars">
                                        <div class="month-bar activities">
                                            <div class="bar-label">نشاطات</div>
                                            <div class="bar-container">
                                                <div class="bar-fill" style="width: <?php echo $totalActivities > 0 ? ($stats['activities'] / max(array_column($monthlyStats, 'activities'))) * 100 : 0; ?>%"></div>
                                            </div>
                                            <div class="bar-value"><?php echo $stats['activities']; ?></div>
                                        </div>
                                        <div class="month-bar beneficiaries">
                                            <div class="bar-label">مستفيدين</div>
                                            <div class="bar-container">
                                                <div class="bar-fill" style="width: <?php echo $totalBeneficiaries > 0 ? ($stats['beneficiaries'] / max(array_column($monthlyStats, 'beneficiaries'))) * 100 : 0; ?>%"></div>
                                            </div>
                                            <div class="bar-value"><?php echo number_format($stats['beneficiaries']); ?></div>
                                        </div>
                                        <div class="month-bar budget">
                                            <div class="bar-label">ميزانية</div>
                                            <div class="bar-container">
                                                <div class="bar-fill" style="width: <?php echo $totalBudget > 0 ? ($stats['budget'] / max(array_column($monthlyStats, 'budget'))) * 100 : 0; ?>%"></div>
                                            </div>
                                            <div class="bar-value"><?php echo number_format($stats['budget']); ?></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="widget">
                        <h3><i class="fas fa-tasks"></i> المهام السريعة</h3>
                        <div class="quick-actions">
                            <a href="activities.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة نشاط جديد
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="widget">
                    <h3><i class="fas fa-history"></i> النشاطات الأخيرة</h3>
                    <div class="recent-activities">
                        <?php if (empty($activities)): ?>
                            <div class="no-data">
                                <i class="fas fa-info-circle"></i>
                                <p>لا توجد نشاطات مسجلة</p>
                                <a href="activities.php" class="btn btn-primary">أضف نشاط جديد</a>
                            </div>
                        <?php else: ?>
                            <?php foreach (array_slice($activities, 0, 5) as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-calendar-check"></i>
                                    </div>
                                    <div class="activity-info">
                                        <h4><?php echo htmlspecialchars($activity['title']); ?></h4>
                                        <p><?php echo htmlspecialchars($activity['description']); ?></p>
                                        <div class="activity-meta">
                                            <small>
                                                <i class="fas fa-clock"></i> 
                                                <?php echo date('Y-m-d', strtotime($activity['activity_date'])); ?>
                                            </small>
                                            <?php if (!empty($activity['location'])): ?>
                                                <small>
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <?php echo htmlspecialchars($activity['location']); ?>
                                                </small>
                                            <?php endif; ?>
                                            <?php if ($activity['beneficiaries_count'] > 0): ?>
                                                <small>
                                                    <i class="fas fa-users"></i>
                                                    <?php echo number_format($activity['beneficiaries_count']); ?> مستفيد
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="activity-status">
                                        <span class="status <?php echo $activity['status']; ?>">
                                            <?php 
                                            $statusTexts = [
                                                'completed' => 'مكتمل',
                                                'in_progress' => 'جاري',
                                                'pending' => 'قادم',
                                                'cancelled' => 'ملغى'
                                            ];
                                            echo $statusTexts[$activity['status']] ?? $activity['status'];
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            <div class="view-all">
                                <a href="activities.php" class="btn btn-outline">عرض جميع النشاطات</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

<style>
.welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
}

.welcome-banner h2 {
    margin: 0 0 10px 0;
    font-size: 1.8rem;
}

.welcome-banner p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.dashboard-overview h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    display: flex;
    align-items: center;
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-left: 4px solid #e74c3c;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card.primary { border-left-color: #3498db; }
.stat-card.success { border-left-color: #2ecc71; }
.stat-card.warning { border-left-color: #f39c12; }
.stat-card.info { border-left-color: #17a2b8; }
.stat-card.secondary { border-left-color: #6c757d; }
.stat-card.danger { border-left-color: #e74c3c; }

.stat-icon {
    font-size: 3rem;
    margin-left: 20px;
    opacity: 0.8;
    color: #667eea;
}

.stat-info h3 {
    font-size: 2.5rem;
    margin: 0;
    font-weight: bold;
    color: #2c3e50;
    border: none;
    padding: 0;
}

.stat-info p {
    margin: 5px 0;
    color: #7f8c8d;
    font-weight: 600;
    font-size: 1rem;
}

.stat-info small {
    color: #95a5a6;
    font-size: 0.85rem;
}

.dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.widget {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.widget h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.status-distribution {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-bar {
    flex: 1;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
}

.status-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.status-fill.completed { background: #2ecc71; }
.status-fill.in-progress { background: #f39c12; }
.status-fill.pending { background: #3498db; }
.status-fill.cancelled { background: #e74c3c; }

.status-info {
    display: flex;
    flex-direction: column;
    min-width: 80px;
}

.status-label {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.status-count {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

.achievement-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.achievement-icon {
    font-size: 2rem;
    color: #667eea;
    width: 50px;
    text-align: center;
}

.achievement-info h4 {
    margin: 0;
    font-size: 1.5rem;
    color: #2c3e50;
}

.achievement-info p {
    margin: 5px 0;
    color: #7f8c8d;
    font-weight: 600;
}

.achievement-info small {
    color: #95a5a6;
}

.categories-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.category-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.category-name {
    min-width: 120px;
    font-weight: 600;
    color: #2c3e50;
}

.category-count {
    min-width: 30px;
    text-align: center;
    font-weight: bold;
    color: #667eea;
}

.category-bar {
    flex: 1;
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.category-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.quick-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.activity-meta {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 5px;
}

.activity-meta small {
    color: #7f8c8d;
    font-size: 0.85rem;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.view-all {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ecf0f1;
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* Advanced Statistics Styles */
.advanced-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.stat-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-value.success { color: #28a745; }
.stat-value.warning { color: #ffc107; }
.stat-value.info { color: #17a2b8; }
.stat-value.primary { color: #007bff; }
.stat-value.secondary { color: #6c757d; }
.stat-value.danger { color: #dc3545; }

.stat-description {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Monthly Trends Styles */
.monthly-trends {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.month-stat {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.month-label {
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
    color: #495057;
    font-size: 0.9rem;
}

.month-bars {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.month-bar {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
}

.bar-label {
    width: 60px;
    font-weight: 500;
    color: #6c757d;
}

.bar-container {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.month-bar.activities .bar-fill { background: #007bff; }
.month-bar.beneficiaries .bar-fill { background: #28a745; }
.month-bar.budget .bar-fill { background: #ffc107; }

.bar-value {
    width: 40px;
    text-align: right;
    font-weight: 500;
    color: #495057;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .achievement-stats {
        gap: 15px;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .activity-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .stat-row {
        grid-template-columns: 1fr;
    }
    
    .monthly-trends {
        grid-template-columns: 1fr;
    }
    
    .month-bar {
        font-size: 0.75rem;
    }
    
    .bar-label {
        width: 50px;
    }
    
    .bar-value {
        width: 35px;
    }
}
</style>

<?php renderAdminFooter(); ?>
