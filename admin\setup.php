<?php
require_once __DIR__ . '/config.php';

// CREATE USER 'ngo'@'localhost' IDENTIFIED BY 'Ngo1234@#charity';


define('DB_HOST', 'localhost');
define('DB_USER', 'ngo');
define('DB_PASS', 'Ngo1234@#charity');
define('DB_NAME', 'ngo_charity');

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    
    // Connect to the new database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create tables
    
    // Activities table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            activity_date DATE,
            image_path VARCHAR(255),
            status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // News table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS news (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            image_path VARCHAR(255),
            published BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Beneficiaries table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS beneficiaries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            family_size INT,
            address TEXT,
            phone VARCHAR(20),
            needs TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Donations table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS donations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            donor_name VARCHAR(255),
            amount DECIMAL(10,2),
            donation_type ENUM('money', 'goods', 'services') DEFAULT 'money',
            description TEXT,
            date_received DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // Volunteers table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS volunteers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            phone VARCHAR(20),
            skills TEXT,
            availability TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Insert default settings
    $pdo->exec("
        INSERT IGNORE INTO settings (setting_key, setting_value) VALUES 
        ('organization_name', 'رابطة أعن بإحسان الخيرية'),
        ('email', '<EMAIL>'),
        ('address', 'الموصل - العراق - حي الكفاءات الثانية'),
        ('phone', ''),
        ('facebook', ''),
        ('instagram', ''),
        ('twitter', '')
    ");
    
    // Insert sample data
    
    // Sample activities
    $pdo->exec("
        INSERT IGNORE INTO activities (id, title, description, activity_date, status) VALUES 
        (1, 'توزيع مساعدات شتوية', 'توزيع بطانيات ومدافئ للعوائل المحتاجة في الأحياء الفقيرة', '2024-01-15', 'completed'),
        (2, 'حملة توعية صحية', 'حملة توعية حول النظافة الشخصية والوقاية من الأمراض', '2024-02-10', 'pending'),
        (3, 'إفطار رمضاني', 'توزيع وجبات إفطار مجانية للصائمين', '2024-03-20', 'pending')
    ");
    
    // Sample beneficiaries
    $pdo->exec("
        INSERT IGNORE INTO beneficiaries (id, name, family_size, address, needs) VALUES 
        (1, 'عائلة أحمد محمد', 6, 'حي الشفاء', 'مساعدات غذائية ومالية'),
        (2, 'عائلة فاطمة علي', 4, 'حي النور', 'مساعدات طبية'),
        (3, 'عائلة سعد حسن', 8, 'حي السلام', 'مساعدات تعليمية وغذائية')
    ");
    
    // Sample donations
    $pdo->exec("
        INSERT IGNORE INTO donations (id, donor_name, amount, donation_type, description, date_received) VALUES 
        (1, 'محسن مجهول', 500000, 'money', 'تبرع نقدي للعوائل المحتاجة', '2024-01-10'),
        (2, 'تاجر محلي', 200000, 'goods', 'مواد غذائية متنوعة', '2024-01-15'),
        (3, 'طبيب متطوع', 0, 'services', 'خدمات طبية مجانية', '2024-01-20')
    ");
    
    echo "تم إنشاء قاعدة البيانات والجداول بنجاح!<br>";
    echo "يمكنك الآن استخدام لوحة التحكم.<br>";
    echo "<a href='login.php'>الذهاب إلى صفحة تسجيل الدخول</a>";
    
} catch(PDOException $e) {
    echo "خطأ في إنشاء قاعدة البيانات: " . $e->getMessage();
}
?>
