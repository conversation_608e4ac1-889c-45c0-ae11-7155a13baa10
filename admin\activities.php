<?php
session_start();
require_once 'admin_header.php';
require_once __DIR__ . '/config.php'; // أو db_config.php إذا كنت تستخدمه

// فحص الاتصال بقاعدة البيانات
$conn = @new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die('<div style="color:red;font-weight:bold;padding:20px;text-align:center;">❌ فشل الاتصال بقاعدة البيانات: ' . $conn->connect_error . '</div>');
} else {
    echo '<div style="color:green;font-weight:bold;padding:20px;text-align:center;">✅ تم الاتصال بقاعدة البيانات بنجاح</div>';
}

// Handle AJAX request for getting activity data
if (isset($_GET['get_activity']) && isset($_GET['id'])) {
    header('Content-Type: application/json');
    $activityId = (int)$_GET['id'];
    
    try {
        $activity = getActivityByIdFromDB($activityId);
        
        if ($activity) {
            echo json_encode([
                'success' => true,
                'data' => $activity
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'النشاط غير موجود'
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
        ]);
    }
    exit;
}

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_activity') {
        // Handle activity addition
        $activityData = [
            'title' => $_POST['activity_title'] ?? '',
            'description' => $_POST['activity_description'] ?? '',
            'content' => $_POST['activity_content'] ?? $_POST['activity_description'] ?? '',
            'activity_date' => $_POST['activity_date'] ?? '',
            'location' => $_POST['activity_location'] ?? '',
            'beneficiaries_count' => $_POST['beneficiaries_count'] ?? 0,
            'budget' => $_POST['budget'] ?? 0,
            'status' => $_POST['activity_status'] ?? 'pending',
            'category' => $_POST['activity_category'] ?? 'general',
            'featured' => isset($_POST['featured']) ? true : false
        ];
        
        // Handle image upload
        if (isset($_FILES['activity_image']) && $_FILES['activity_image']['error'] === UPLOAD_ERR_OK) {
            $imagePath = uploadActivityImage($_FILES['activity_image']);
            if ($imagePath) {
                $activityData['image'] = $imagePath;
            } else {
                $_SESSION['error_message'] = "فشل في رفع الصورة";
                header("Location: " . $_SERVER['PHP_SELF']);
                exit;
            }
        }
        
        $result = addActivity($activityData);
        if ($result) {
            $_SESSION['success_message'] = "تم إضافة النشاط بنجاح";
        } else {
            $_SESSION['error_message'] = "فشل في إضافة النشاط - قد يكون النشاط موجود مسبقاً بنفس العنوان والتاريخ";
        }
        
        // Redirect to prevent form resubmission
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'edit_activity') {
        // Handle activity editing
        $activityId = $_POST['activity_id'] ?? 0;
        $activityData = [
            'title' => $_POST['activity_title'] ?? '',
            'description' => $_POST['activity_description'] ?? '',
            'content' => $_POST['activity_content'] ?? $_POST['activity_description'] ?? '',
            'activity_date' => $_POST['activity_date'] ?? '',
            'location' => $_POST['activity_location'] ?? '',
            'beneficiaries_count' => $_POST['beneficiaries_count'] ?? 0,
            'budget' => $_POST['budget'] ?? 0,
            'status' => $_POST['activity_status'] ?? 'pending',
            'category' => $_POST['activity_category'] ?? 'general',
            'featured' => isset($_POST['featured']) ? true : false
        ];
        
        // Handle image upload if new image is provided
        if (isset($_FILES['activity_image']) && $_FILES['activity_image']['error'] === UPLOAD_ERR_OK) {
            $imagePath = uploadActivityImage($_FILES['activity_image']);
            if ($imagePath) {
                $activityData['image'] = $imagePath;
            } else {
                $_SESSION['error_message'] = "فشل في رفع الصورة";
                header("Location: " . $_SERVER['PHP_SELF']);
                exit;
            }
        }
        
        if ($activityId && updateActivity($activityId, $activityData)) {
            $_SESSION['success_message'] = "تم تحديث النشاط بنجاح";
        } else {
            $_SESSION['error_message'] = "فشل في تحديث النشاط";
        }
        
        // Redirect to prevent form resubmission
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'delete_activity') {
        $activityId = $_POST['activity_id'] ?? 0;
        if ($activityId && deleteActivity($activityId)) {
            $_SESSION['success_message'] = "تم حذف النشاط بنجاح";
        } else {
            $_SESSION['error_message'] = "فشل في حذف النشاط";
        }
        
        // Redirect to prevent form resubmission
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
}

// Get messages from session
$success_message = $_SESSION['success_message'] ?? '';
$error_message = $_SESSION['error_message'] ?? '';

// Clear messages from session
unset($_SESSION['success_message']);
unset($_SESSION['error_message']);

// Get activities for display
$activities = getActivities();

renderAdminHeader('إدارة النشاطات');
?>

            <?php if (!empty($success_message)): ?>
                <?php echo showAlert($success_message, 'success'); ?>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <?php echo showAlert($error_message, 'error'); ?>
            <?php endif; ?>

            <!-- Activities Management -->
            <div class="admin-section active">
                <h2><i class="fas fa-calendar-alt"></i> إدارة النشاطات</h2>
                
                <div class="section-info">
                    <p><i class="fas fa-info-circle"></i> يمكنك إضافة نشاط جديد أو تعديل/حذف النشاطات الموجودة من خلال أزرار الإجراءات في الجدول</p>
                </div>
                
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="toggleForm('activity-form')">
                        <i class="fas fa-plus"></i> إضافة نشاط جديد
                    </button>
                </div>

                <div id="activity-form" class="form-container" style="display: none;">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="add_activity">
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="activity_title">عنوان النشاط *</label>
                                <input type="text" id="activity_title" name="activity_title" required>
                            </div>
                            <div class="form-group">
                                <label for="activity_category">فئة النشاط</label>
                                <select id="activity_category" name="activity_category">
                                    <option value="general">عام</option>
                                    <option value="food">مساعدات غذائية</option>
                                    <option value="health">صحي</option>
                                    <option value="education">تعليمي</option>
                                    <option value="social">اجتماعي</option>
                                    <option value="emergency">طوارئ</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="activity_description">وصف مختصر *</label>
                            <textarea id="activity_description" name="activity_description" rows="3" required placeholder="وصف مختصر للنشاط"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="activity_content">التفاصيل الكاملة</label>
                            <textarea id="activity_content" name="activity_content" rows="5" placeholder="تفاصيل كاملة عن النشاط وما تم إنجازه"></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="activity_date">تاريخ النشاط *</label>
                                <input type="date" id="activity_date" name="activity_date" required>
                            </div>
                            <div class="form-group">
                                <label for="activity_status">حالة النشاط</label>
                                <select id="activity_status" name="activity_status">
                                    <option value="pending">قادم</option>
                                    <option value="in_progress">جاري التنفيذ</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغى</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="activity_location">الموقع</label>
                            <input type="text" id="activity_location" name="activity_location" placeholder="مكان تنفيذ النشاط">
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="beneficiaries_count">عدد المستفيدين</label>
                                <input type="number" id="beneficiaries_count" name="beneficiaries_count" min="0" placeholder="0">
                            </div>
                            <div class="form-group">
                                <label for="budget">الميزانية (دينار عراقي)</label>
                                <input type="number" id="budget" name="budget" min="0" step="1000" placeholder="0">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="activity_image">صورة النشاط</label>
                                <input type="file" id="activity_image" name="activity_image" accept="image/*">
                                <small>اختياري - يُفضل صور بجودة عالية</small>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="featured"> نشاط مميز
                                    <span class="checkmark"></span>
                                </label>
                                <small>سيظهر في المقدمة على الموقع</small>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ النشاط
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="toggleForm('activity-form')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Edit Activity Form -->
                <div id="edit-activity-form" class="form-container" style="display: none;">
                    <h3><i class="fas fa-edit"></i> تعديل النشاط</h3>
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="edit_activity">
                        <input type="hidden" name="activity_id" id="edit_activity_id">
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit_activity_title">عنوان النشاط *</label>
                                <input type="text" id="edit_activity_title" name="activity_title" required>
                            </div>
                            <div class="form-group">
                                <label for="edit_activity_category">فئة النشاط</label>
                                <select id="edit_activity_category" name="activity_category">
                                    <option value="general">عام</option>
                                    <option value="food">مساعدات غذائية</option>
                                    <option value="health">صحي</option>
                                    <option value="education">تعليمي</option>
                                    <option value="social">اجتماعي</option>
                                    <option value="emergency">طوارئ</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit_activity_description">وصف مختصر *</label>
                            <textarea id="edit_activity_description" name="activity_description" rows="3" required placeholder="وصف مختصر للنشاط"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit_activity_content">التفاصيل الكاملة</label>
                            <textarea id="edit_activity_content" name="activity_content" rows="5" placeholder="تفاصيل كاملة عن النشاط وما تم إنجازه"></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit_activity_date">تاريخ النشاط *</label>
                                <input type="date" id="edit_activity_date" name="activity_date" required>
                            </div>
                            <div class="form-group">
                                <label for="edit_activity_status">حالة النشاط</label>
                                <select id="edit_activity_status" name="activity_status">
                                    <option value="pending">قادم</option>
                                    <option value="in_progress">جاري التنفيذ</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغى</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit_activity_location">الموقع</label>
                            <input type="text" id="edit_activity_location" name="activity_location" placeholder="مكان تنفيذ النشاط">
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit_beneficiaries_count">عدد المستفيدين</label>
                                <input type="number" id="edit_beneficiaries_count" name="beneficiaries_count" min="0" placeholder="0">
                            </div>
                            <div class="form-group">
                                <label for="edit_budget">الميزانية (دينار عراقي)</label>
                                <input type="number" id="edit_budget" name="budget" min="0" step="1000" placeholder="0">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit_activity_image">صورة النشاط</label>
                                <input type="file" id="edit_activity_image" name="activity_image" accept="image/*">
                                <small>اختياري - اتركه فارغاً للاحتفاظ بالصورة الحالية</small>
                                <div id="current_image_info" style="margin-top: 5px; font-size: 12px; color: #666;"></div>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="featured" id="edit_featured"> نشاط مميز
                                    <span class="checkmark"></span>
                                </label>
                                <small>سيظهر في المقدمة على الموقع</small>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="toggleForm('edit-activity-form')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Delete Confirmation Popup Modal -->
                <div id="deleteConfirmationModal" class="modal-overlay" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3><i class="fas fa-exclamation-triangle"></i> تأكيد الحذف</h3>
                        </div>
                        <div class="modal-body">
                            <p class="confirmation-text">هل أنت متأكد من حذف هذا النشاط؟</p>
                            <div class="activity-info">
                                <p><i class="fas fa-calendar"></i> <strong>العنوان:</strong> <span id="deleteActivityTitle"></span></p>
                                <p><i class="fas fa-clock"></i> <strong>التاريخ:</strong> <span id="deleteActivityDate"></span></p>
                            </div>
                            <div class="warning-message">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                                <i class="fas fa-trash"></i> نعم، احذف
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="hideDeleteConfirmation()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </div>
                </div>

                <div class="data-table">
                    <table>
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>الفئة</th>
                                <th>التاريخ</th>
                                <th>المستفيدين</th>
                                <th>الحالة</th>
                                <th>مميز</th>
                                <th><i class="fas fa-cogs"></i> الإجراءات (تعديل/حذف)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($activities)): ?>
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد نشاطات مسجلة</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($activities as $activity): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($activity['title']); ?></strong>
                                            <?php if (!empty($activity['location'])): ?>
                                                <br><small><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($activity['location']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php 
                                            $categories = [
                                                'general' => 'عام',
                                                'food' => 'مساعدات غذائية',
                                                'health' => 'صحي',
                                                'education' => 'تعليمي',
                                                'social' => 'اجتماعي',
                                                'emergency' => 'طوارئ'
                                            ];
                                            echo $categories[$activity['category']] ?? $activity['category'];
                                            ?>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($activity['activity_date'])); ?></td>
                                        <td><?php echo number_format($activity['beneficiaries_count']); ?></td>
                                        <td>
                                            <?php 
                                            $statusClass = '';
                                            $statusText = '';
                                            switch($activity['status']) {
                                                case 'completed':
                                                    $statusClass = 'completed';
                                                    $statusText = 'مكتمل';
                                                    break;
                                                case 'in_progress':
                                                    $statusClass = 'in-progress';
                                                    $statusText = 'جاري';
                                                    break;
                                                case 'pending':
                                                    $statusClass = 'pending';
                                                    $statusText = 'قادم';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'cancelled';
                                                    $statusText = 'ملغى';
                                                    break;
                                                default:
                                                    $statusClass = 'pending';
                                                    $statusText = $activity['status'];
                                            }
                                            ?>
                                            <span class="status <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($activity['featured']): ?>
                                                <i class="fas fa-star text-warning" title="نشاط مميز"></i>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-primary" title="تعديل النشاط" onclick="editActivity(<?php echo $activity['id']; ?>)">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </button>
                                                <button class="btn btn-sm btn-danger" title="حذف النشاط" onclick="showDeleteConfirmation(<?php echo $activity['id']; ?>, '<?php echo htmlspecialchars($activity['title'], ENT_QUOTES); ?>', '<?php echo date('Y-m-d', strtotime($activity['activity_date'])); ?>')">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

<script>
function toggleForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        if (form.style.display === 'none' || form.style.display === '') {
            form.style.display = 'block';
            form.scrollIntoView({ behavior: 'smooth' });
        } else {
            form.style.display = 'none';
        }
    }
}

function editActivity(id) {
    console.log('Editing activity with ID:', id); // Debug log
    
    // Hide add form if open
    const addForm = document.getElementById('activity-form');
    if (addForm) addForm.style.display = 'none';
    
    // Show loading state
    const imageInfo = document.getElementById('current_image_info');
    if (imageInfo) {
        imageInfo.innerHTML = '<i class="fas fa-spinner fa-spin text-primary"></i> جاري تحميل بيانات النشاط...';
    }
    
    // Use fetch to get activity data
    const url = `activities.php?get_activity=1&id=${id}`;
    console.log('Fetching from URL:', url); // Debug log
    
    fetch(url)
        .then(response => {
            console.log('Response status:', response.status); // Debug log
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data); // Debug log
            
            if (data.success) {
                const activity = data.data;
                
                // Populate edit form
                document.getElementById('edit_activity_id').value = activity.id;
                document.getElementById('edit_activity_title').value = activity.title || '';
                document.getElementById('edit_activity_description').value = activity.description || '';
                document.getElementById('edit_activity_content').value = activity.content || '';
                document.getElementById('edit_activity_date').value = activity.activity_date || '';
                document.getElementById('edit_activity_location').value = activity.location || '';
                document.getElementById('edit_beneficiaries_count').value = activity.beneficiaries_count || '';
                document.getElementById('edit_budget').value = activity.budget || '';
                document.getElementById('edit_activity_status').value = activity.status || 'pending';
                document.getElementById('edit_activity_category').value = activity.category || 'general';
                document.getElementById('edit_featured').checked = activity.featured == 1;
                
                // Show current image info
                if (imageInfo) {
                    if (activity.image_url) {
                        imageInfo.innerHTML = `<i class="fas fa-image text-success"></i> الصورة الحالية: ${activity.image_url.split('/').pop()} - سيتم الاحتفاظ بها إذا لم تختر صورة جديدة`;
                    } else {
                        imageInfo.innerHTML = '<i class="fas fa-info-circle text-info"></i> لا توجد صورة حالية - يمكنك إضافة صورة جديدة';
                    }
                }
                
                // Show edit form
                toggleForm('edit-activity-form');
                
                // Focus on title field
                setTimeout(() => {
                    document.getElementById('edit_activity_title').focus();
                }, 100);
                
            } else {
                console.error('Server error:', data.message); // Debug log
                alert('فشل في تحميل بيانات النشاط: ' + (data.message || 'خطأ غير معروف'));
                if (imageInfo) {
                    imageInfo.innerHTML = '<i class="fas fa-exclamation-triangle text-danger"></i> فشل في تحميل البيانات: ' + data.message;
                }
            }
        })
        .catch(error => {
            console.error('Fetch error:', error); // Debug log
            alert('حدث خطأ في تحميل بيانات النشاط: ' + error.message);
            if (imageInfo) {
                imageInfo.innerHTML = '<i class="fas fa-exclamation-triangle text-danger"></i> خطأ في الشبكة: ' + error.message;
            }
        });
}

function deleteActivity(activityId) {
    // Create a form to submit the delete request
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';
    
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'delete_activity';
    
    const idInput = document.createElement('input');
    idInput.type = 'hidden';
    idInput.name = 'activity_id';
    idInput.value = activityId;
    
    form.appendChild(actionInput);
    form.appendChild(idInput);
    
    document.body.appendChild(form);
    form.submit();
}

function showDeleteConfirmation(activityId, activityTitle, activityDate) {
    // Populate modal with activity info
    document.getElementById('deleteActivityTitle').textContent = activityTitle;
    document.getElementById('deleteActivityDate').textContent = activityDate;
    
    // Show modal
    const modal = document.getElementById('deleteConfirmationModal');
    modal.style.display = 'flex';
    
    // Set up confirm button click handler
    document.getElementById('confirmDeleteBtn').onclick = function() {
        hideDeleteConfirmation();
        deleteActivity(activityId);
    };
    
    // Focus on cancel button for safety
    setTimeout(() => {
        const cancelBtn = modal.querySelector('.btn-secondary');
        if (cancelBtn) cancelBtn.focus();
    }, 100);
}

function hideDeleteConfirmation() {
    const modal = document.getElementById('deleteConfirmationModal');
    modal.style.display = 'none';
}

// Enhanced event listeners
document.addEventListener('DOMContentLoaded', function() {
    const deleteModal = document.getElementById('deleteConfirmationModal');
    
    // Delete modal event listeners
    if (deleteModal) {
        // Close modal when clicking outside of it
        deleteModal.addEventListener('click', function(e) {
            if (e.target === deleteModal) {
                hideDeleteConfirmation();
            }
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && deleteModal.style.display === 'flex') {
                hideDeleteConfirmation();
            }
        });
    }
});
</script>

<?php renderAdminFooter(); ?>
