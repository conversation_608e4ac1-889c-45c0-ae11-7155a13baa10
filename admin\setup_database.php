<?php
require_once __DIR__ . '/config.php';
/**
 * Database Setup with Correct Structure
 */

echo "<h2>إعداد قاعدة البيانات - البنية المحدثة</h2>";

try {
    // Connect to MySQL server
    $mysqli = new mysqli('localhost', 'root', '');
    
    if ($mysqli->connect_error) {
        throw new Exception("فشل الاتصال بخادم MySQL: " . $mysqli->connect_error);
    }
    
    echo "✅ تم الاتصال بخادم MySQL بنجاح<br>";
    
    // Create database
    $mysqli->query("CREATE DATABASE IF NOT EXISTS ngo_charity CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات 'ngo_charity' بنجاح<br>";
    
    // Select database
    $mysqli->select_db('ngo_charity');
    
    // Drop existing activities table if exists (to recreate with correct structure)
    $mysqli->query("DROP TABLE IF EXISTS activities");
    echo "🔄 تم حذف الجدول القديم للإنشاء مجدداً<br>";
    
    // Create activities table with the exact structure you specified
    $createTable = "
    CREATE TABLE activities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL COMMENT 'عنوان النشاط',
        description TEXT COMMENT 'وصف النشاط',
        content TEXT COMMENT 'محتوى النشاط التفصيلي',
        image_url VARCHAR(500) COMMENT 'رابط صورة النشاط',
        activity_date DATE COMMENT 'تاريخ النشاط',
        location VARCHAR(255) COMMENT 'مكان النشاط',
        beneficiaries_count INT DEFAULT 0 COMMENT 'عدد المستفيدين',
        budget DECIMAL(12,2) DEFAULT 0.00 COMMENT 'ميزانية النشاط',
        status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT 'حالة النشاط',
        category VARCHAR(100) DEFAULT 'general' COMMENT 'فئة النشاط',
        featured BOOLEAN DEFAULT FALSE COMMENT 'نشاط مميز',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by VARCHAR(100) DEFAULT 'admin' COMMENT 'المستخدم الذي أنشأ النشاط'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($mysqli->query($createTable)) {
        echo "✅ تم إنشاء جدول 'activities' بالبنية الصحيحة<br>";
    } else {
        throw new Exception("فشل في إنشاء الجدول: " . $mysqli->error);
    }
    
    // Insert sample activities data
    $sampleData = [
        [
            'title' => 'توزيع المساعدات الغذائية',
            'description' => 'توزيع الطرود الغذائية على الأسر المحتاجة في الموصل',
            'content' => 'نقوم بتوزيع الطرود الغذائية التي تحتوي على مواد أساسية مثل الأرز والسكر والزيت والدقيق وغيرها من المواد الضرورية للأسر المحتاجة والفقيرة في مدينة الموصل وضواحيها.',
            'image_url' => '',
            'activity_date' => '2025-01-15',
            'location' => 'الموصل - أحياء مختلفة',
            'beneficiaries_count' => 150,
            'budget' => 25000.00,
            'status' => 'completed',
            'category' => 'food',
            'featured' => 1,
            'created_by' => 'admin'
        ],
        [
            'title' => 'حملة التطعيم المجاني',
            'description' => 'حملة تطعيم مجانية للأطفال في المناطق النائية',
            'content' => 'تنظيم حملة تطعيم مجانية بالتعاون مع وزارة الصحة لتطعيم الأطفال ضد الأمراض المعدية في المناطق النائية والمحرومة.',
            'image_url' => '',
            'activity_date' => '2025-02-20',
            'location' => 'قرى وأطراف الموصل',
            'beneficiaries_count' => 300,
            'budget' => 15000.00,
            'status' => 'completed',
            'category' => 'health',
            'featured' => 1,
            'created_by' => 'admin'
        ],
        [
            'title' => 'مشروع كفالة الأيتام',
            'description' => 'برنامج شهري لكفالة ومتابعة الأطفال الأيتام',
            'content' => 'برنامج شامل لكفالة الأيتام يشمل الدعم المالي الشهري، المتابعة التعليمية، الرعاية الصحية، والأنشطة الترفيهية والتربوية.',
            'image_url' => '',
            'activity_date' => '2025-01-01',
            'location' => 'مختلف أنحاء الموصل',
            'beneficiaries_count' => 75,
            'budget' => 45000.00,
            'status' => 'in_progress',
            'category' => 'social',
            'featured' => 1,
            'created_by' => 'admin'
        ],
        [
            'title' => 'دورة تعليم الحاسوب',
            'description' => 'دورات مجانية لتعليم أساسيات الحاسوب والإنترنت',
            'content' => 'دورات تدريبية مجانية لتعليم الشباب والشابات أساسيات استخدام الحاسوب والإنترنت لمساعدتهم في إيجاد فرص عمل أفضل.',
            'image_url' => '',
            'activity_date' => '2025-09-10',
            'location' => 'مقر الرابطة',
            'beneficiaries_count' => 40,
            'budget' => 8000.00,
            'status' => 'pending',
            'category' => 'education',
            'featured' => 0,
            'created_by' => 'admin'
        ]
    ];
    
    $insertQuery = "INSERT INTO activities (title, description, content, image_url, activity_date, location, beneficiaries_count, budget, status, category, featured, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $mysqli->prepare($insertQuery);
    
    if ($stmt) {
        foreach ($sampleData as $data) {
            $stmt->bind_param("sssssiidssis", 
                $data['title'],
                $data['description'],
                $data['content'],
                $data['image_url'],
                $data['activity_date'],
                $data['location'],
                $data['beneficiaries_count'],
                $data['budget'],
                $data['status'],
                $data['category'],
                $data['featured'],
                $data['created_by']
            );
            $stmt->execute();
        }
        $stmt->close();
        echo "✅ تم إدراج البيانات التجريبية بنجاح (" . count($sampleData) . " نشاط)<br>";
    }
    
    // Also create/recreate news table
    $mysqli->query("DROP TABLE IF EXISTS news");
    
    $createNewsTable = "
    CREATE TABLE news (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        summary TEXT,
        content TEXT NOT NULL,
        news_date DATE,
        status ENUM('published', 'draft', 'scheduled') DEFAULT 'published',
        featured BOOLEAN DEFAULT FALSE,
        image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($mysqli->query($createNewsTable)) {
        echo "✅ تم إنشاء جدول 'news' بنجاح<br>";
    }
    
    $mysqli->close();
    
    echo "<br><strong>🎉 تم إعداد قاعدة البيانات بالبنية الصحيحة!</strong><br>";
    echo "<br>البنية النهائية للجدول:<br>";
    echo "• id - المعرف الفريد<br>";
    echo "• title - عنوان النشاط<br>";
    echo "• description - وصف النشاط<br>";
    echo "• content - محتوى النشاط التفصيلي<br>";
    echo "• image_url - رابط صورة النشاط<br>";
    echo "• activity_date - تاريخ النشاط<br>";
    echo "• location - مكان النشاط<br>";
    echo "• beneficiaries_count - عدد المستفيدين<br>";
    echo "• budget - ميزانية النشاط<br>";
    echo "• status - حالة النشاط<br>";
    echo "• category - فئة النشاط<br>";
    echo "• featured - نشاط مميز<br>";
    echo "• created_at - تاريخ الإنشاء<br>";
    echo "• updated_at - تاريخ التحديث<br>";
    echo "• created_by - المستخدم الذي أنشأ النشاط<br>";
    
    echo "<br>يمكنك الآن:<br>";
    echo "• عرض النشاطات: <a href='../activities.php' target='_blank'>../activities.php</a><br>";
    echo "• إدارة النشاطات: <a href='activities.php' target='_blank'>activities.php</a><br>";
    echo "• إدارة الأخبار: <a href='news.php' target='_blank'>news.php</a><br>";
    echo "• لوحة التحكم: <a href='dashboard.php' target='_blank'>dashboard.php</a><br>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "تأكد من:<br>";
    echo "• تشغيل خدمة MySQL في XAMPP<br>";
    echo "• صحة بيانات الاتصال (افتراضي: root بدون كلمة مرور)<br>";
}
?>
