<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// Include activities manager
require_once 'activities_manager.php';

// Initialize activities file
initializeActivitiesFile();

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_news':
                // Add news logic here
                $success_message = "تم إضافة الخبر بنجاح";
                break;
                
            case 'add_activity':
                // Handle activity addition
                $activityData = [
                    'title' => $_POST['activity_title'] ?? '',
                    'description' => $_POST['activity_description'] ?? '',
                    'content' => $_POST['activity_content'] ?? $_POST['activity_description'] ?? '',
                    'activity_date' => $_POST['activity_date'] ?? '',
                    'location' => $_POST['activity_location'] ?? '',
                    'beneficiaries_count' => $_POST['beneficiaries_count'] ?? 0,
                    'budget' => $_POST['budget'] ?? 0,
                    'status' => $_POST['activity_status'] ?? 'pending',
                    'category' => $_POST['activity_category'] ?? 'general',
                    'featured' => isset($_POST['featured']) ? true : false
                ];
                
                // Handle image upload
                if (isset($_FILES['activity_image']) && $_FILES['activity_image']['error'] === UPLOAD_ERR_OK) {
                    $imagePath = uploadActivityImage($_FILES['activity_image']);
                    if ($imagePath) {
                        $activityData['image'] = $imagePath;
                    } else {
                        $error_message = "فشل في رفع الصورة";
                    }
                }
                
                if (empty($error_message)) {
                    $result = addActivity($activityData);
                    if ($result) {
                        $success_message = "تم إضافة النشاط بنجاح";
                    } else {
                        $error_message = "فشل في إضافة النشاط";
                    }
                }
                break;
                
            case 'update_contact':
                // Update contact info logic here
                $success_message = "تم تحديث معلومات التواصل بنجاح";
                break;
        }
    }
}

// Get real statistics from activities
$activitiesStats = getActivitiesStats();
$stats = [
    'total_activities' => $activitiesStats['total'],
    'total_beneficiaries' => $activitiesStats['total_beneficiaries'],
    'total_donations' => $activitiesStats['total_budget'],
    'total_volunteers' => 30 // This can be added later
];

// Get activities for display
$activities = getActivities();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - رابطة أعن بإحسان الخيرية</title>
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="admin-style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="admin-body">
    <div class="admin-wrapper">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <a href="../index.php" class="logo-link">
                        <img src="../logo.png" alt="شعار رابطة أعن بإحسان الخيرية" class="sidebar-logo">
                    </a>
                </div>
                <h2><i class="fas fa-cogs"></i> لوحة التحكم</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#dashboard" class="nav-item active"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                    <li><a href="#activities" class="nav-item"><i class="fas fa-calendar-alt"></i> إدارة النشاطات</a></li>
                    <li><a href="#news" class="nav-item"><i class="fas fa-newspaper"></i> إدارة الأخبار</a></li>
                    <li><a href="#beneficiaries" class="nav-item"><i class="fas fa-users"></i> المستفيدين</a></li>
                    <li><a href="#donations" class="nav-item"><i class="fas fa-hand-holding-heart"></i> التبرعات</a></li>
                    <li><a href="#volunteers" class="nav-item"><i class="fas fa-user-friends"></i> المتطوعين</a></li>
                    <li><a href="#settings" class="nav-item"><i class="fas fa-cog"></i> الإعدادات</a></li>
                    <li><a href="../index.php" class="nav-item"><i class="fas fa-home"></i> العودة للموقع</a></li>
                    <li><a href="logout.php" class="nav-item logout"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <h1>مرحباً بك في لوحة التحكم</h1>
                <div class="admin-user-info">
                    <span>مرحباً، المدير</span>
                    <time><?php echo date('Y-m-d H:i'); ?></time>
                </div>
            </header>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($db_error)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    لم يتم العثور على قاعدة البيانات. يرجى إنشاء قاعدة بيانات باسم "ngo_db" أو تحديث إعدادات الاتصال.
                </div>
            <?php endif; ?>

            <!-- Dashboard Content -->
            <div id="dashboard" class="admin-section active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $stats['total_activities']; ?></h3>
                            <p>إجمالي النشاطات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $stats['total_beneficiaries']; ?></h3>
                            <p>المستفيدين</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($stats['total_donations']); ?> د.ع</h3>
                            <p>إجمالي التبرعات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-friends"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $stats['total_volunteers']; ?></h3>
                            <p>المتطوعين</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-widgets">
                    <div class="widget">
                        <h3><i class="fas fa-chart-line"></i> إحصائيات سريعة</h3>
                        <div class="quick-stats">
                            <div class="quick-stat">
                                <span class="number">5</span>
                                <span class="label">نشاطات هذا الشهر</span>
                            </div>
                            <div class="quick-stat">
                                <span class="number">12</span>
                                <span class="label">عائلة مستفيدة هذا الأسبوع</span>
                            </div>
                            <div class="quick-stat">
                                <span class="number">3</span>
                                <span class="label">متطوعين جدد</span>
                            </div>
                        </div>
                    </div>

                    <div class="widget">
                        <h3><i class="fas fa-tasks"></i> المهام السريعة</h3>
                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="showSection('activities')">
                                <i class="fas fa-plus"></i> إضافة نشاط جديد
                            </button>
                            <button class="btn btn-secondary" onclick="showSection('news')">
                                <i class="fas fa-plus"></i> إضافة خبر جديد
                            </button>
                            <button class="btn btn-success" onclick="showSection('beneficiaries')">
                                <i class="fas fa-user-plus"></i> إضافة مستفيد
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activities Management -->
            <div id="activities" class="admin-section">
                <h2><i class="fas fa-calendar-alt"></i> إدارة النشاطات</h2>
                
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="toggleForm('activity-form')">
                        <i class="fas fa-plus"></i> إضافة نشاط جديد
                    </button>
                </div>

                <div id="activity-form" class="form-container" style="display: none;">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="add_activity">
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="activity_title">عنوان النشاط *</label>
                                <input type="text" id="activity_title" name="activity_title" required>
                            </div>
                            <div class="form-group">
                                <label for="activity_category">فئة النشاط</label>
                                <select id="activity_category" name="activity_category">
                                    <option value="general">عام</option>
                                    <option value="food">مساعدات غذائية</option>
                                    <option value="health">صحي</option>
                                    <option value="education">تعليمي</option>
                                    <option value="social">اجتماعي</option>
                                    <option value="emergency">طوارئ</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="activity_description">وصف مختصر *</label>
                            <textarea id="activity_description" name="activity_description" rows="3" required placeholder="وصف مختصر للنشاط"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="activity_content">التفاصيل الكاملة</label>
                            <textarea id="activity_content" name="activity_content" rows="5" placeholder="تفاصيل كاملة عن النشاط وما تم إنجازه"></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="activity_date">تاريخ النشاط *</label>
                                <input type="date" id="activity_date" name="activity_date" required>
                            </div>
                            <div class="form-group">
                                <label for="activity_status">حالة النشاط</label>
                                <select id="activity_status" name="activity_status">
                                    <option value="pending">قادم</option>
                                    <option value="in_progress">جاري التنفيذ</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغى</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="activity_location">الموقع</label>
                            <input type="text" id="activity_location" name="activity_location" placeholder="مكان تنفيذ النشاط">
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="beneficiaries_count">عدد المستفيدين</label>
                                <input type="number" id="beneficiaries_count" name="beneficiaries_count" min="0" placeholder="0">
                            </div>
                            <div class="form-group">
                                <label for="budget">الميزانية (دينار عراقي)</label>
                                <input type="number" id="budget" name="budget" min="0" step="1000" placeholder="0">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="activity_image">صورة النشاط</label>
                                <input type="file" id="activity_image" name="activity_image" accept="image/*">
                                <small>اختياري - يُفضل صور بجودة عالية</small>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="featured"> نشاط مميز
                                    <span class="checkmark"></span>
                                </label>
                                <small>سيظهر في المقدمة على الموقع</small>
                            </div>
                        </div>
                            <input type="date" id="activity_date" name="activity_date" required>
                        </div>
                        <div class="form-group">
                            <label for="activity_image">صورة النشاط</label>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ النشاط
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="toggleForm('activity-form')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>

                <div class="data-table">
                    <table>
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>الفئة</th>
                                <th>التاريخ</th>
                                <th>المستفيدين</th>
                                <th>الحالة</th>
                                <th>مميز</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($activities)): ?>
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد نشاطات مسجلة</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($activities as $activity): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($activity['title']); ?></strong>
                                            <?php if (!empty($activity['location'])): ?>
                                                <br><small><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($activity['location']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php 
                                            $categories = [
                                                'general' => 'عام',
                                                'food' => 'مساعدات غذائية',
                                                'health' => 'صحي',
                                                'education' => 'تعليمي',
                                                'social' => 'اجتماعي',
                                                'emergency' => 'طوارئ'
                                            ];
                                            echo $categories[$activity['category']] ?? $activity['category'];
                                            ?>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($activity['activity_date'])); ?></td>
                                        <td><?php echo number_format($activity['beneficiaries_count']); ?></td>
                                        <td>
                                            <?php 
                                            $statusClass = '';
                                            $statusText = '';
                                            switch($activity['status']) {
                                                case 'completed':
                                                    $statusClass = 'completed';
                                                    $statusText = 'مكتمل';
                                                    break;
                                                case 'in_progress':
                                                    $statusClass = 'in-progress';
                                                    $statusText = 'جاري';
                                                    break;
                                                case 'pending':
                                                    $statusClass = 'pending';
                                                    $statusText = 'قادم';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'cancelled';
                                                    $statusText = 'ملغى';
                                                    break;
                                                default:
                                                    $statusClass = 'pending';
                                                    $statusText = $activity['status'];
                                            }
                                            ?>
                                            <span class="status <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($activity['featured']): ?>
                                                <i class="fas fa-star text-warning" title="نشاط مميز"></i>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button class="btn-icon edit" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete" title="حذف" onclick="showDeleteDialog(<?php echo $activity['id']; ?>, '<?php echo htmlspecialchars($activity['title'], ENT_QUOTES); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- News Management -->
            <div id="news" class="admin-section">
                <h2><i class="fas fa-newspaper"></i> إدارة الأخبار</h2>
                
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="toggleForm('news-form')">
                        <i class="fas fa-plus"></i> إضافة خبر جديد
                    </button>
                </div>

                <div id="news-form" class="form-container" style="display: none;">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="add_news">
                        <div class="form-group">
                            <label for="news_title">عنوان الخبر</label>
                            <input type="text" id="news_title" name="news_title" required>
                        </div>
                        <div class="form-group">
                            <label for="news_content">محتوى الخبر</label>
                            <textarea id="news_content" name="news_content" rows="6" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="news_image">صورة الخبر</label>
                            <input type="file" id="news_image" name="news_image" accept="image/*">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">نشر الخبر</button>
                            <button type="button" class="btn btn-secondary" onclick="toggleForm('news-form')">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Other sections placeholder -->
            <div id="beneficiaries" class="admin-section">
                <h2><i class="fas fa-users"></i> إدارة المستفيدين</h2>
                <p>قريباً...</p>
            </div>

            <div id="donations" class="admin-section">
                <h2><i class="fas fa-hand-holding-heart"></i> إدارة التبرعات</h2>
                <p>قريباً...</p>
            </div>

            <div id="volunteers" class="admin-section">
                <h2><i class="fas fa-user-friends"></i> إدارة المتطوعين</h2>
                <p>قريباً...</p>
            </div>

            <div id="settings" class="admin-section">
                <h2><i class="fas fa-cog"></i> الإعدادات</h2>
                <div class="settings-grid">
                    <div class="setting-card">
                        <h3>معلومات التواصل</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="update_contact">
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <input type="email" id="email" name="email" value="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="phone">رقم الهاتف</label>
                                <input type="tel" id="phone" name="phone">
                            </div>
                            <div class="form-group">
                                <label for="address">العنوان</label>
                                <input type="text" id="address" name="address" value="الموصل - العراق - حي الكفاءات الثانية">
                            </div>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="admin-script.js"></script>
    <script>
        // Backup navigation script in case external file fails
        document.addEventListener('DOMContentLoaded', function() {
            // Manual navigation setup
            const navItems = document.querySelectorAll('.sidebar-nav .nav-item');
            const sections = document.querySelectorAll('.admin-section');
            
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    const href = this.getAttribute('href');
                    
                    // Skip external links
                    if (!href || !href.startsWith('#')) {
                        return;
                    }
                    
                    e.preventDefault();
                    
                    // Get target section
                    const target = href.substring(1);
                    
                    // Hide all sections
                    sections.forEach(section => {
                        section.classList.remove('active');
                    });
                    
                    // Show target section
                    const targetSection = document.getElementById(target);
                    if (targetSection) {
                        targetSection.classList.add('active');
                    }
                    
                    // Update active nav item
                    navItems.forEach(nav => nav.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // Make sure dashboard is shown by default
            const dashboardSection = document.getElementById('dashboard');
            if (dashboardSection) {
                dashboardSection.classList.add('active');
            }
        });
        
        // Global function for quick actions buttons
        function showSection(sectionId) {
            const sections = document.querySelectorAll('.admin-section');
            const navItems = document.querySelectorAll('.sidebar-nav .nav-item');
            
            // Hide all sections
            sections.forEach(section => {
                section.classList.remove('active');
            });
            
            // Show target section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }
            
            // Update navigation
            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === '#' + sectionId) {
                    item.classList.add('active');
                }
            });
        }
        
        // Global function for form toggles
        function toggleForm(formId) {
            const form = document.getElementById(formId);
            if (form) {
                if (form.style.display === 'none' || !form.style.display) {
                    form.style.display = 'block';
                    form.scrollIntoView({ behavior: 'smooth' });
                } else {
                    form.style.display = 'none';
                }
            }
        }

        // Custom delete confirmation dialog
        function showDeleteDialog(activityId, activityTitle) {
            const dialog = document.getElementById('deleteDialog');
            const activityName = document.getElementById('activityName');
            const confirmBtn = document.getElementById('confirmDelete');
            
            // Set activity name with animation
            activityName.textContent = activityTitle;
            activityName.style.animation = 'none';
            setTimeout(() => {
                activityName.style.animation = 'shimmer 2s infinite';
            }, 100);
            
            // Show dialog with backdrop blur
            dialog.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            // Add shake animation to dialog content
            const dialogContent = dialog.querySelector('.delete-dialog-content');
            setTimeout(() => {
                dialogContent.style.animation = 'slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            }, 50);
            
            // Set up confirm button with enhanced click handler
            confirmBtn.onclick = function() {
                // Add loading state
                confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
                confirmBtn.disabled = true;
                
                setTimeout(() => {
                    deleteActivity(activityId);
                    hideDeleteDialog();
                }, 1000);
            };
        }

        function hideDeleteDialog() {
            const dialog = document.getElementById('deleteDialog');
            const dialogContent = dialog.querySelector('.delete-dialog-content');
            const confirmBtn = document.getElementById('confirmDelete');
            
            // Reset button state
            confirmBtn.innerHTML = '<i class="fas fa-trash-alt"></i> نعم، احذف النشاط';
            confirmBtn.disabled = false;
            
            // Animate out
            dialogContent.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                dialog.style.display = 'none';
                document.body.style.overflow = 'auto';
                dialogContent.style.animation = '';
            }, 300);
        }

        function deleteActivity(activityId) {
            const row = document.querySelector(`button[onclick*="${activityId}"]`).closest('tr');
            if (row) {
                // Add delete animation
                row.style.background = 'linear-gradient(90deg, #ffebee, #ffcdd2)';
                row.style.animation = 'shake 0.5s ease-in-out, fadeOut 0.8s ease-out 0.5s';
                row.style.transform = 'scale(0.95)';
                
                setTimeout(() => {
                    row.remove();
                    showAlert('✅ تم حذف النشاط بنجاح', 'success');
                    
                    // Update statistics if needed
                    updateActivityStats();
                }, 1300);
            }
        }

        function updateActivityStats() {
            // Update the activities count in the dashboard
            const totalActivities = document.querySelectorAll('.data-table tbody tr').length - 1; // -1 for header
            const statsElement = document.querySelector('.stat-card h3');
            if (statsElement) {
                statsElement.textContent = totalActivities;
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                animation: slideIn 0.5s ease-out;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                border-radius: 12px;
                font-weight: 600;
            `;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
            `;
            
            document.body.appendChild(alert);
            
            // Auto remove after 4 seconds
            setTimeout(() => {
                alert.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => alert.remove(), 300);
            }, 4000);
        }

        // Close dialog when clicking outside or pressing Escape
        document.getElementById('deleteDialog').addEventListener('click', function(e) {
            if (e.target === this) {
                hideDeleteDialog();
            }
        });

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideDeleteDialog();
            }
        });
    </script>

    <!-- Custom Delete Confirmation Dialog -->
    <div id="deleteDialog" class="delete-dialog">
        <div class="delete-dialog-content">
            <div class="delete-dialog-header">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>تأكيد حذف النشاط</h3>
            </div>
            <div class="delete-dialog-body">
                <p>هل أنت متأكد من رغبتك في حذف النشاط التالي؟</p>
                <strong id="activityName"></strong>
                <p class="warning-text">سيتم حذف النشاط نهائياً ولن يمكن استرداده!</p>
            </div>
            <div class="delete-dialog-footer">
                <button id="confirmDelete" class="btn btn-danger">
                    <i class="fas fa-trash-alt"></i> نعم، احذف النشاط
                </button>
                <button onclick="hideDeleteDialog()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> إلغاء العملية
                </button>
            </div>
        </div>
    </div>
</body>
</html>