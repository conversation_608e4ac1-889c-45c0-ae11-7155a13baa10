-- إنشاء قاعدة البيانات للمنظمة الخيرية
CREATE DATABASE IF NOT EXISTS ngo_charity CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ngo_charity;

-- جدو<PERSON> النشاطات
CREATE TABLE IF NOT EXISTS activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT 'عنوان النشاط',
    description TEXT NOT NULL COMMENT 'وصف النشاط',
    content LONGTEXT NOT NULL COMMENT 'محتوى النشاط التفصيلي',
    image_url VARCHAR(500) DEFAULT NULL COMMENT 'رابط صورة النشاط',
    activity_date DATE NOT NULL COMMENT 'تاريخ النشاط',
    location VARCHAR(255) DEFAULT NULL COMMENT 'مكان النشاط',
    beneficiaries_count INT DEFAULT 0 COMMENT 'عدد المستفيدين',
    budget DECIMAL(10,2) DEFAULT 0.00 COMMENT 'ميزانية النشاط',
    status ENUM('planned', 'ongoing', 'completed', 'cancelled') DEFAULT 'planned' COMMENT 'حالة النشاط',
    category ENUM('health', 'education', 'food', 'shelter', 'social', 'emergency', 'other') DEFAULT 'other' COMMENT 'فئة النشاط',
    featured BOOLEAN DEFAULT FALSE COMMENT 'نشاط مميز',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT DEFAULT NULL COMMENT 'المستخدم الذي أنشأ النشاط'
);

-- جدول فئات النشاطات
CREATE TABLE IF NOT EXISTS activity_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'اسم الفئة',
    description TEXT COMMENT 'وصف الفئة',
    icon VARCHAR(100) DEFAULT NULL COMMENT 'أيقونة الفئة',
    color VARCHAR(7) DEFAULT '#007bff' COMMENT 'لون الفئة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول صور النشاطات
CREATE TABLE IF NOT EXISTS activity_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_title VARCHAR(255) DEFAULT NULL,
    is_main BOOLEAN DEFAULT FALSE COMMENT 'الصورة الرئيسية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE
);

-- جدول المتطوعين
CREATE TABLE IF NOT EXISTS volunteers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'اسم المتطوع',
    email VARCHAR(255) UNIQUE DEFAULT NULL COMMENT 'بريد إلكتروني',
    phone VARCHAR(20) DEFAULT NULL COMMENT 'رقم الهاتف',
    address TEXT COMMENT 'العنوان',
    skills TEXT COMMENT 'المهارات',
    availability TEXT COMMENT 'أوقات التوفر',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending' COMMENT 'حالة المتطوع',
    joined_date DATE DEFAULT NULL COMMENT 'تاريخ الانضمام',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط المتطوعين بالنشاطات
CREATE TABLE IF NOT EXISTS activity_volunteers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    volunteer_id INT NOT NULL,
    role VARCHAR(100) DEFAULT NULL COMMENT 'الدور في النشاط',
    hours_worked DECIMAL(5,2) DEFAULT 0.00 COMMENT 'ساعات العمل',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE,
    FOREIGN KEY (volunteer_id) REFERENCES volunteers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_activity_volunteer (activity_id, volunteer_id)
);

-- جدول المستفيدين
CREATE TABLE IF NOT EXISTS beneficiaries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'اسم المستفيد',
    national_id VARCHAR(20) UNIQUE DEFAULT NULL COMMENT 'رقم الهوية',
    phone VARCHAR(20) DEFAULT NULL COMMENT 'رقم الهاتف',
    address TEXT COMMENT 'العنوان',
    family_size INT DEFAULT 1 COMMENT 'حجم الأسرة',
    income_level ENUM('none', 'low', 'medium') DEFAULT 'low' COMMENT 'مستوى الدخل',
    special_needs TEXT COMMENT 'احتياجات خاصة',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط المستفيدين بالنشاطات
CREATE TABLE IF NOT EXISTS activity_beneficiaries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    beneficiary_id INT NOT NULL,
    assistance_type VARCHAR(255) DEFAULT NULL COMMENT 'نوع المساعدة',
    amount_received DECIMAL(10,2) DEFAULT 0.00 COMMENT 'قيمة المساعدة',
    notes TEXT COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE,
    FOREIGN KEY (beneficiary_id) REFERENCES beneficiaries(id) ON DELETE CASCADE
);

-- جدول التبرعات
CREATE TABLE IF NOT EXISTS donations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    donor_name VARCHAR(255) NOT NULL COMMENT 'اسم المتبرع',
    donor_email VARCHAR(255) DEFAULT NULL,
    donor_phone VARCHAR(20) DEFAULT NULL,
    amount DECIMAL(10,2) NOT NULL COMMENT 'مبلغ التبرع',
    currency VARCHAR(3) DEFAULT 'IQD' COMMENT 'العملة',
    donation_type ENUM('money', 'goods', 'services') DEFAULT 'money' COMMENT 'نوع التبرع',
    purpose VARCHAR(255) DEFAULT NULL COMMENT 'الغرض من التبرع',
    activity_id INT DEFAULT NULL COMMENT 'النشاط المخصص له التبرع',
    is_anonymous BOOLEAN DEFAULT FALSE COMMENT 'تبرع مجهول',
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE SET NULL
);

-- جدول المستخدمين الإداريين
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role ENUM('admin', 'manager', 'editor') DEFAULT 'editor',
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج بيانات تجريبية للفئات
INSERT INTO activity_categories (name, description, icon, color) VALUES
('الصحة', 'الأنشطة المتعلقة بالرعاية الصحية والطبية', 'fas fa-heartbeat', '#e74c3c'),
('التعليم', 'الأنشطة التعليمية والتدريبية', 'fas fa-graduation-cap', '#3498db'),
('الغذاء', 'توزيع الطعام والمساعدات الغذائية', 'fas fa-utensils', '#27ae60'),
('المأوى', 'المساعدة في السكن والإيواء', 'fas fa-home', '#f39c12'),
('الاجتماعية', 'الأنشطة الاجتماعية والثقافية', 'fas fa-users', '#9b59b6'),
('الطوارئ', 'الإغاثة في حالات الطوارئ', 'fas fa-exclamation-triangle', '#e67e22'),
('أخرى', 'أنشطة متنوعة أخرى', 'fas fa-star', '#34495e');

-- إدراج مستخدم إداري افتراضي
INSERT INTO admin_users (username, password, full_name, email, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin');
-- كلمة المرور: password

-- إدراج بيانات تجريبية للنشاطات
INSERT INTO activities (title, description, content, activity_date, location, beneficiaries_count, budget, status, category, featured) VALUES
('توزيع المساعدات الغذائية', 'توزيع سلل غذائية على العائلات المحتاجة', 'تم تنظيم حملة لتوزيع السلل الغذائية على 100 عائلة محتاجة في منطقة حي الكفاءات. تضمنت السلة الغذائية الأرز والعدس والسكر والشاي وزيت الطبخ والطحين. تم التوزيع بالتعاون مع المتطوعين وبحضور ممثلين من المجتمع المحلي.', '2024-12-15', 'حي الكفاءات الثانية - الموصل', 100, 2500000.00, 'completed', 'food', TRUE),

('حملة التبرع بالدم', 'تنظيم حملة للتبرع بالدم لصالح مستشفيات المدينة', 'نظمت الرابطة حملة للتبرع بالدم بالتعاون مع بنك الدم المركزي في الموصل. شارك في الحملة أكثر من 80 متبرعاً، وتم جمع 75 كيس دم ساهمت في إنقاذ حياة العديد من المرضى. تضمنت الحملة أيضاً توعية حول أهمية التبرع بالدم.', '2024-12-10', 'مركز الرابطة - الموصل', 80, 500000.00, 'completed', 'health', TRUE),

('توزيع الملابس الشتوية', 'توزيع ملابس شتوية للأطفال والعائلات المحتاجة', 'مع بداية فصل الشتاء، قامت الرابطة بتوزيع ملابس شتوية دافئة شملت معاطف وأحذية وقفازات للأطفال والكبار. استفاد من هذه الحملة 150 شخصاً من مختلف الأعمار، مما ساهم في حمايتهم من برد الشتاء القارس.', '2024-12-20', 'عدة مناطق في الموصل', 150, 3000000.00, 'completed', 'shelter', FALSE),

('دورة تدريبية في الحاسوب', 'دورة مجانية لتعليم أساسيات الحاسوب والإنترنت', 'تم تنظيم دورة تدريبية مجانية لمدة 3 أسابيع لتعليم أساسيات استخدام الحاسوب والإنترنت. شارك في الدورة 25 شاباً وشابة، وتم تقديم شهادات مشاركة للمتدربين. هدفت الدورة إلى تطوير مهارات الشباب وزيادة فرصهم في العمل.', '2025-01-15', 'مركز التدريب - الموصل', 25, 800000.00, 'ongoing', 'education', FALSE),

('إفطار صائم رمضان', 'توزيع وجبات إفطار للصائمين في رمضان', 'حملة سنوية لتوزيع وجبات إفطار مجانية للصائمين والعائلات المحتاجة خلال شهر رمضان المبارك. يتم التحضير للحملة من خلال جمع التبرعات وتنظيم فرق من المتطوعين لإعداد وتوزيع الوجبات يومياً.', '2025-03-15', 'مسجد الحي - الموصل', 200, 5000000.00, 'planned', 'food', TRUE);

-- فهارس لتحسين الأداء
CREATE INDEX idx_activities_date ON activities(activity_date);
CREATE INDEX idx_activities_status ON activities(status);
CREATE INDEX idx_activities_category ON activities(category);
CREATE INDEX idx_activities_featured ON activities(featured);
CREATE INDEX idx_donations_amount ON donations(amount);
CREATE INDEX idx_donations_date ON donations(created_at);

-- إنشاء view لعرض إحصائيات سريعة
CREATE VIEW activity_stats AS
SELECT 
    category,
    COUNT(*) as total_activities,
    SUM(beneficiaries_count) as total_beneficiaries,
    SUM(budget) as total_budget,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_activities,
    COUNT(CASE WHEN status = 'ongoing' THEN 1 END) as ongoing_activities,
    COUNT(CASE WHEN status = 'planned' THEN 1 END) as planned_activities
FROM activities 
GROUP BY category;

-- إنشاء view للنشاطات المميزة
CREATE VIEW featured_activities AS
SELECT 
    id, title, description, image_url, activity_date, location, 
    beneficiaries_count, status, category
FROM activities 
WHERE featured = TRUE 
ORDER BY activity_date DESC;
