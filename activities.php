<?php
echo '<pre>'; print_r($activities); echo '</pre>';
ini_set('display_errors', 1);
error_reporting(E_ALL);
session_start(); 
require_once __DIR__ . '/admin/activities_manager.php';

// Get activities from database/file
$activities = getActivities();
$featuredActivities = getFeaturedActivities();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نشاطات الرابطة - رابطة أعن بإحسان الخيرية</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.php" class="logo-link">
                        <img src="logo.png" alt="شعار الرابطة" class="nav-logo">
                        <span class="nav-title">رابطة أعن بإحسان</span>
                    </a>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.php" class="nav-link">الرئيسية</a></li>
                    <li><a href="about.php" class="nav-link">عن الرابطة</a></li>
                    <li><a href="activities.php" class="nav-link active">النشاطات</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <section class="page-header">
            <div class="container">
                <h1>نشاطات رابطة أعن بإحسان الخيرية</h1>
                <p>تعرف على جميع الأنشطة والبرامج التي تقوم بها الرابطة لخدمة المجتمع</p>
            </div>
        </section>

        <section class="activities-content">
            <div class="container">
                <?php if (!empty($featuredActivities)): ?>
                    <div class="featured-activities">
                        <h2>النشاطات المميزة</h2>
                        <div class="activities-grid">
                            <?php foreach ($featuredActivities as $activity): ?>
                                <div class="activity-card featured">
                                    <?php if (!empty($activity['image_url'])): ?>
                                        <div class="activity-image">
                                            <img src="<?php echo htmlspecialchars($activity['image_url']); ?>" alt="<?php echo htmlspecialchars($activity['title']); ?>" class="clickable-image" onclick="openImageModal(this)">
                                        </div>
                                    <?php elseif (!empty($activity['image'])): ?>
                                        <div class="activity-image">
                                            <img src="<?php echo htmlspecialchars($activity['image']); ?>" alt="<?php echo htmlspecialchars($activity['title']); ?>" class="clickable-image" onclick="openImageModal(this)">
                                        </div>
                                    <?php else: ?>
                                        <div class="activity-icon">
                                            <?php 
                                            $icons = [
                                                'food' => '🍞',
                                                'health' => '🏥',
                                                'education' => '�',
                                                'social' => '🤝',
                                                'emergency' => '🚨',
                                                'general' => '🎯'
                                            ];
                                            echo $icons[$activity['category']] ?? '🎯';
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                    <h3><?php echo htmlspecialchars($activity['title']); ?></h3>
                                    <p><?php echo htmlspecialchars($activity['description']); ?></p>
                                    <div class="activity-details">
                                        <?php if (!empty($activity['location'])): ?>
                                            <span class="tag location">📍 <?php echo htmlspecialchars($activity['location']); ?></span>
                                        <?php endif; ?>
                                        <span class="tag date"><?php echo date('Y-m-d', strtotime($activity['activity_date'])); ?></span>
                                        <?php if ($activity['beneficiaries_count'] > 0): ?>
                                            <span class="tag beneficiaries">👥 <?php echo number_format($activity['beneficiaries_count']); ?> مستفيد</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="all-activities">
                    <h2>جميع النشاطات</h2>
                    <div class="activities-grid">
                        <?php if (empty($activities)): ?>
                            <div class="no-activities">
                                <p>لا توجد نشاطات مسجلة حالياً</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($activities as $activity): ?>
                                <div class="activity-card <?php echo $activity['status']; ?>">
                                    <?php if (!empty($activity['image_url'])): ?>
                                        <div class="activity-image">
                                            <img src="<?php echo htmlspecialchars($activity['image_url']); ?>" alt="<?php echo htmlspecialchars($activity['title']); ?>" class="clickable-image" onclick="openImageModal(this)">
                                        </div>
                                    <?php elseif (!empty($activity['image'])): ?>
                                        <div class="activity-image">
                                            <img src="<?php echo htmlspecialchars($activity['image']); ?>" alt="<?php echo htmlspecialchars($activity['title']); ?>" class="clickable-image" onclick="openImageModal(this)">
                                        </div>
                                    <?php else: ?>
                                        <div class="activity-icon">
                                            <?php 
                                            $icons = [
                                                'food' => '🍞',
                                                'health' => '🏥',
                                                'education' => '📚',
                                                'social' => '🤝',
                                                'emergency' => '🚨',
                                                'general' => '🎯'
                                            ];
                                            echo $icons[$activity['category']] ?? '🎯';
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                    <h3><?php echo htmlspecialchars($activity['title']); ?></h3>
                                    <p><?php echo htmlspecialchars($activity['description']); ?></p>
                                    
                                    <?php if (!empty($activity['content']) && $activity['content'] !== $activity['description']): ?>
                                        <div class="activity-content">
                                            <p><?php echo nl2br(htmlspecialchars($activity['content'])); ?></p>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="activity-details">
                                        <?php if (!empty($activity['location'])): ?>
                                            <span class="tag location">📍 <?php echo htmlspecialchars($activity['location']); ?></span>
                                        <?php endif; ?>
                                        <span class="tag date">📅 <?php echo date('Y-m-d', strtotime($activity['activity_date'])); ?></span>
                                        <?php if ($activity['beneficiaries_count'] > 0): ?>
                                            <span class="tag beneficiaries">👥 <?php echo number_format($activity['beneficiaries_count']); ?> مستفيد</span>
                                        <?php endif; ?>
                                        <span class="tag status status-<?php echo $activity['status']; ?>">
                                            <?php 
                                            $statusTexts = [
                                                'completed' => 'مكتمل',
                                                'in_progress' => 'جاري التنفيذ',
                                                'pending' => 'قادم',
                                                'cancelled' => 'ملغى'
                                            ];
                                            echo $statusTexts[$activity['status']] ?? $activity['status'];
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- قسم الإحصائيات -->
                <section class="statistics">
                    <h2>إحصائيات أنشطتنا</h2>
                    <div class="stats-grid">
                        <?php 
                        $activitiesStats = getActivitiesStats();
                        ?>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($activitiesStats['total_beneficiaries']); ?>+</div>
                            <div class="stat-label">مستفيد</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($activitiesStats['total']); ?></div>
                            <div class="stat-label">نشاط منجز</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($activitiesStats['completed']); ?></div>
                            <div class="stat-label">نشاط مكتمل</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($activitiesStats['this_month']); ?></div>
                            <div class="stat-label">نشاط هذا الشهر</div>
                        </div>
                    </div>
                </section>

                <!-- قسم المساهمة -->
                <section class="contribute">
                    <div class="contribute-card">
                        <h2>ساهم معنا</h2>
                        <p>يمكنك المساهمة في أنشطتنا من خلال التبرع أو التطوع معنا</p>
                        <div class="contact-methods">
                            <p><strong>للتواصل:</strong></p>
                            <p>📧 <EMAIL></p>
                            <p>📍 الموصل - العراق - حي الكفاءات الثانية - قرب جامع الفردوس</p>
                        </div>
                    </div>
                </section>

            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 رابطة أعن بإحسان الخيرية - جميع الحقوق محفوظة</p>
            <p>الموصل - العراق - حي الكفاءات الثانية</p>
            <p class="developer-credit">Developed by Moyasar M. Hasan</p>
        </div>
    </footer>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <div class="image-modal-content">
            <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="">
            <div class="image-modal-caption" id="imageCaption"></div>
        </div>
    </div>

    <script>
        function openImageModal(img) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            const captionText = document.getElementById('imageCaption');
            
            modal.style.display = 'flex';
            modalImg.src = img.src;
            modalImg.alt = img.alt;
            captionText.textContent = img.alt;
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
            
            // Restore body scroll
            document.body.style.overflow = 'auto';
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });

        // Add cursor pointer and hover effects to clickable images
        document.addEventListener('DOMContentLoaded', function() {
            const clickableImages = document.querySelectorAll('.clickable-image');
            clickableImages.forEach(img => {
                img.style.cursor = 'pointer';
                img.title = 'انقر لتكبير الصورة';
            });
        });
    </script>

</body>
</html>
