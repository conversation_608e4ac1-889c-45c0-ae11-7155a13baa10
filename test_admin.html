<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Navigation</title>
</head>
<body>
    <h1>Admin Panel Navigation Test</h1>
    
    <div style="margin: 20px 0;">
        <button onclick="testNavigation()">Test Navigation</button>
        <button onclick="checkElements()">Check Elements</button>
    </div>
    
    <div id="results" style="background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px;">
        <p>Click the buttons above to test the admin panel navigation.</p>
    </div>

    <script>
        function testNavigation() {
            const results = document.getElementById('results');
            
            // Try to access the admin panel in iframe
            const iframe = document.createElement('iframe');
            iframe.src = 'admin/index.php';
            iframe.style.width = '100%';
            iframe.style.height = '400px';
            iframe.style.border = '1px solid #ccc';
            
            results.innerHTML = '<h3>Loading Admin Panel...</h3>';
            results.appendChild(iframe);
            
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument;
                    const navItems = iframeDoc.querySelectorAll('.sidebar-nav .nav-item');
                    const sections = iframeDoc.querySelectorAll('.admin-section');
                    
                    results.innerHTML = `
                        <h3>Navigation Test Results:</h3>
                        <p><strong>Navigation Items Found:</strong> ${navItems.length}</p>
                        <p><strong>Sections Found:</strong> ${sections.length}</p>
                        <p><strong>Active Section:</strong> ${iframeDoc.querySelector('.admin-section.active')?.id || 'None'}</p>
                        <p><strong>JavaScript Loaded:</strong> ${iframe.contentWindow.showSection ? 'Yes' : 'No'}</p>
                    `;
                } catch (e) {
                    results.innerHTML = `<p style="color: red;">Error accessing iframe: ${e.message}</p>`;
                }
            };
        }
        
        function checkElements() {
            const results = document.getElementById('results');
            
            fetch('admin/index.php')
                .then(response => response.text())
                .then(html => {
                    const navItemsCount = (html.match(/class="nav-item"/g) || []).length;
                    const sectionsCount = (html.match(/class="admin-section"/g) || []).length;
                    const hasScript = html.includes('admin-script.js');
                    
                    results.innerHTML = `
                        <h3>HTML Structure Check:</h3>
                        <p><strong>Navigation Items in HTML:</strong> ${navItemsCount}</p>
                        <p><strong>Admin Sections in HTML:</strong> ${sectionsCount}</p>
                        <p><strong>Script Included:</strong> ${hasScript ? 'Yes' : 'No'}</p>
                        <p><strong>Page Length:</strong> ${html.length} characters</p>
                    `;
                })
                .catch(error => {
                    results.innerHTML = `<p style="color: red;">Error fetching page: ${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
