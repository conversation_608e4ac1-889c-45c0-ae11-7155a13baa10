<?php
/**
 * سكريپت إعداد قاعدة البيانات للاستضافة
 * يجب تشغيل هذا السكريپت مرة واحدة فقط بعد رفع الموقع
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>إعداد قاعدة البيانات للاستضافة</h1>";

// تحميل إعدادات قاعدة البيانات
require_once 'admin/config.php';

echo "<h2>إعدادات قاعدة البيانات:</h2>";
echo "<p>Host: " . DB_HOST . "</p>";
echo "<p>User: " . DB_USER . "</p>";
echo "<p>Database: " . DB_NAME . "</p>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // إنشاء جدول activities
    echo "<h2>إنشاء جدول النشاطات:</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS activities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL COMMENT 'عنوان النشاط',
        description TEXT COMMENT 'وصف النشاط',
        content TEXT COMMENT 'محتوى النشاط التفصيلي',
        image_url VARCHAR(500) COMMENT 'رابط صورة النشاط',
        activity_date DATE COMMENT 'تاريخ النشاط',
        location VARCHAR(255) COMMENT 'مكان النشاط',
        beneficiaries_count INT DEFAULT 0 COMMENT 'عدد المستفيدين',
        budget DECIMAL(12,2) DEFAULT 0.00 COMMENT 'ميزانية النشاط',
        status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT 'حالة النشاط',
        category VARCHAR(100) DEFAULT 'general' COMMENT 'فئة النشاط',
        featured BOOLEAN DEFAULT FALSE COMMENT 'نشاط مميز',
        image VARCHAR(255) DEFAULT '' COMMENT 'مسار الصورة',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by VARCHAR(100) DEFAULT 'admin'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول activities بنجاح</p>";
    
    // فحص وجود بيانات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM activities");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        echo "<h2>إدراج البيانات التجريبية:</h2>";
        
        // إدراج بيانات من ملف JSON إذا كان موجوداً
        $jsonFile = 'database/data/activities.json';
        if (file_exists($jsonFile)) {
            $jsonContent = file_get_contents($jsonFile);
            $activities = json_decode($jsonContent, true);
            
            if ($activities && is_array($activities)) {
                $insertSql = "
                INSERT INTO activities (
                    title, description, content, activity_date, location, 
                    beneficiaries_count, budget, status, category, featured, image
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ";
                
                $stmt = $pdo->prepare($insertSql);
                $inserted = 0;
                
                foreach ($activities as $activity) {
                    try {
                        $stmt->execute([
                            $activity['title'],
                            $activity['description'],
                            $activity['content'] ?? $activity['description'],
                            $activity['activity_date'],
                            $activity['location'] ?? '',
                            (int)($activity['beneficiaries_count'] ?? 0),
                            (float)($activity['budget'] ?? 0),
                            $activity['status'] ?? 'pending',
                            $activity['category'] ?? 'general',
                            $activity['featured'] ? 1 : 0,
                            $activity['image'] ?? ''
                        ]);
                        $inserted++;
                    } catch (Exception $e) {
                        echo "<p style='color: orange;'>⚠️ تخطي نشاط: " . $activity['title'] . " - " . $e->getMessage() . "</p>";
                    }
                }
                
                echo "<p style='color: green;'>✅ تم إدراج $inserted نشاط من ملف JSON</p>";
            }
        } else {
            // إدراج بيانات تجريبية أساسية
            $sampleData = [
                [
                    'title' => 'توزيع المساعدات الغذائية',
                    'description' => 'توزيع سلل غذائية على العائلات المحتاجة',
                    'content' => 'تم تنظيم حملة لتوزيع السلل الغذائية على 100 عائلة محتاجة في منطقة حي الكفاءات.',
                    'activity_date' => '2024-12-15',
                    'location' => 'حي الكفاءات الثانية - الموصل',
                    'beneficiaries_count' => 100,
                    'budget' => 2500000.00,
                    'status' => 'completed',
                    'category' => 'food',
                    'featured' => 1
                ],
                [
                    'title' => 'حملة التبرع بالدم',
                    'description' => 'تنظيم حملة للتبرع بالدم لصالح مستشفيات المدينة',
                    'content' => 'نظمت الرابطة حملة للتبرع بالدم بالتعاون مع بنك الدم المركزي في الموصل.',
                    'activity_date' => '2024-12-10',
                    'location' => 'مركز الرابطة - الموصل',
                    'beneficiaries_count' => 80,
                    'budget' => 500000.00,
                    'status' => 'completed',
                    'category' => 'health',
                    'featured' => 1
                ]
            ];
            
            $insertSql = "
            INSERT INTO activities (
                title, description, content, activity_date, location, 
                beneficiaries_count, budget, status, category, featured
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $pdo->prepare($insertSql);
            
            foreach ($sampleData as $data) {
                $stmt->execute([
                    $data['title'],
                    $data['description'],
                    $data['content'],
                    $data['activity_date'],
                    $data['location'],
                    $data['beneficiaries_count'],
                    $data['budget'],
                    $data['status'],
                    $data['category'],
                    $data['featured']
                ]);
            }
            
            echo "<p style='color: green;'>✅ تم إدراج البيانات التجريبية الأساسية</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ الجدول يحتوي على " . $result['count'] . " نشاط مسبقاً</p>";
    }
    
    // إنشاء جدول admin_users إذا لم يكن موجوداً
    echo "<h2>إنشاء جدول المستخدمين الإداريين:</h2>";
    
    $adminSql = "
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        role ENUM('admin', 'manager', 'editor') DEFAULT 'admin',
        status ENUM('active', 'inactive') DEFAULT 'active',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($adminSql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول admin_users بنجاح</p>";
    
    // إنشاء مستخدم إداري افتراضي
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        $stmt = $pdo->prepare("INSERT INTO admin_users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['admin', 'admin123', 'مدير النظام', '<EMAIL>', 'admin']);
        echo "<p style='color: green;'>✅ تم إنشاء المستخدم الإداري الافتراضي</p>";
        echo "<p><strong>اسم المستخدم:</strong> admin</p>";
        echo "<p><strong>كلمة المرور:</strong> admin123</p>";
    }
    
    echo "<h2>✅ تم إعداد قاعدة البيانات بنجاح!</h2>";
    echo "<p><a href='activities.php'>اختبار صفحة النشاطات</a></p>";
    echo "<p><a href='admin/login.php'>دخول لوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إعداد قاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>صحة إعدادات قاعدة البيانات في admin/config.php</li>";
    echo "<li>وجود قاعدة البيانات في الاستضافة</li>";
    echo "<li>صلاحيات المستخدم للوصول لقاعدة البيانات</li>";
    echo "</ul>";
}

echo "<p style='color: red;'><strong>مهم:</strong> احذف هذا الملف بعد الانتهاء من الإعداد!</p>";
?>
