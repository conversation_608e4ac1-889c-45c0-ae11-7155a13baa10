<?php
// Test adding activity
ini_set('display_errors', 1);
error_reporting(E_ALL);
session_start();

echo "<h1>Test Adding Activity</h1>";

require_once __DIR__ . '/admin/activities_manager.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Processing Form Submission</h2>";
    
    $activityData = [
        'title' => $_POST['title'] ?? '',
        'description' => $_POST['description'] ?? '',
        'content' => $_POST['content'] ?? $_POST['description'] ?? '',
        'activity_date' => $_POST['activity_date'] ?? '',
        'location' => $_POST['location'] ?? '',
        'beneficiaries_count' => $_POST['beneficiaries_count'] ?? 0,
        'budget' => $_POST['budget'] ?? 0,
        'status' => $_POST['status'] ?? 'pending',
        'category' => $_POST['category'] ?? 'general',
        'featured' => isset($_POST['featured']) ? true : false
    ];
    
    echo "<h3>Activity Data:</h3>";
    echo "<pre>" . print_r($activityData, true) . "</pre>";
    
    echo "<h3>Adding Activity...</h3>";
    $result = addActivity($activityData);
    
    if ($result) {
        echo "<p style='color: green;'>✅ Activity added successfully with ID: $result</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to add activity</p>";
        if (isset($_SESSION['db_error'])) {
            echo "<p style='color: red;'>Error: " . $_SESSION['db_error'] . "</p>";
            unset($_SESSION['db_error']);
        }
    }
    
    echo "<hr>";
}

// Test database connection
echo "<h2>Database Connection Test</h2>";
$connection = getDatabaseConnection();
if ($connection) {
    echo "<p style='color: green;'>✅ Database connected (" . get_class($connection) . ")</p>";
} else {
    echo "<p style='color: red;'>❌ Database connection failed - will use JSON fallback</p>";
}

// Show current activities count
echo "<h2>Current Activities</h2>";
$activities = getActivities();
echo "<p>Total activities: " . count($activities) . "</p>";

if (!empty($activities)) {
    echo "<h3>Recent activities:</h3>";
    echo "<ul>";
    foreach (array_slice($activities, 0, 5) as $activity) {
        echo "<li>" . $activity['title'] . " (" . $activity['activity_date'] . ")</li>";
    }
    echo "</ul>";
}
?>

<h2>Add New Activity</h2>
<form method="POST" style="max-width: 600px;">
    <div style="margin-bottom: 10px;">
        <label>عنوان النشاط:</label><br>
        <input type="text" name="title" required style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>وصف النشاط:</label><br>
        <textarea name="description" required style="width: 100%; padding: 5px; height: 80px;"></textarea>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>المحتوى التفصيلي:</label><br>
        <textarea name="content" style="width: 100%; padding: 5px; height: 100px;"></textarea>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>تاريخ النشاط:</label><br>
        <input type="date" name="activity_date" required style="padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>المكان:</label><br>
        <input type="text" name="location" style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>عدد المستفيدين:</label><br>
        <input type="number" name="beneficiaries_count" min="0" style="padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>الميزانية:</label><br>
        <input type="number" name="budget" min="0" step="0.01" style="padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>الحالة:</label><br>
        <select name="status" style="padding: 5px;">
            <option value="pending">قادم</option>
            <option value="in_progress">جاري التنفيذ</option>
            <option value="completed">مكتمل</option>
            <option value="cancelled">ملغى</option>
        </select>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>الفئة:</label><br>
        <select name="category" style="padding: 5px;">
            <option value="general">عام</option>
            <option value="food">غذاء</option>
            <option value="health">صحة</option>
            <option value="education">تعليم</option>
            <option value="social">اجتماعي</option>
            <option value="emergency">طوارئ</option>
        </select>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>
            <input type="checkbox" name="featured"> نشاط مميز
        </label>
    </div>
    
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer;">
        إضافة النشاط
    </button>
</form>

<p><a href="activities.php">عرض صفحة النشاطات</a></p>
<p><a href="admin/activities.php">لوحة التحكم - النشاطات</a></p>
