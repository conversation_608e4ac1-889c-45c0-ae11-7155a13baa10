# لوحة التحكم - رابطة أعن بإحسان الخيرية

## نظرة عامة

تم إنشاء لوحة تحكم شاملة لإدارة موقع رابطة أعن بإحسان الخيرية. تتيح هذه اللوحة للمديرين إدارة جميع جوانب الموقع بسهولة.

## الميزات الرئيسية

### 🏠 الرئيسية (Dashboard)
- إحصائيات سريعة للأنشطة والمستفيدين والتبرعات
- نظرة عامة على النشاطات الحديثة
- مهام سريعة للإجراءات الشائعة

### 📅 إدارة النشاطات
- إضافة نشاطات جديدة
- تحرير وحذف النشاطات الموجودة
- رفع الصور للنشاطات
- تتبع حالة النشاطات (قادم/مكتمل/ملغي)

### 📰 إدارة الأخبار
- إضافة أخبار جديدة
- إدارة محتوى الأخبار
- رفع الصور للأخبار

### 👥 إدارة المستفيدين
- تسجيل المستفيدين الجدد
- تتبع حجم العائلات واحتياجاتهم
- إدارة معلومات الاتصال

### 💝 إدارة التبرعات
- تسجيل التبرعات النقدية والعينية
- تتبع المتبرعين
- إحصائيات التبرعات

### 🤝 إدارة المتطوعين
- تسجيل المتطوعين الجدد
- إدارة مهارات وتوفر المتطوعين
- متابعة أنشطة التطوع

### ⚙️ الإعدادات
- تحديث معلومات التواصل
- إدارة معلومات المنظمة
- إعدادات عامة للموقع

## متطلبات التشغيل

- **خادم ويب**: Apache/Nginx
- **PHP**: الإصدار 7.4 أو أحدث
- **قاعدة البيانات**: MySQL 5.7 أو أحدث
- **XAMPP**: للتطوير المحلي

## التثبيت والإعداد

### 1. إعداد البيئة المحلية

1. تأكد من تشغيل XAMPP (Apache + MySQL)
2. ضع ملفات المشروع في مجلد `htdocs`

### 2. إعداد قاعدة البيانات

1. انتقل إلى: `http://localhost/ngo/admin/setup.php`
2. ستقوم الصفحة بإنشاء قاعدة البيانات والجداول تلقائياً
3. ستُدرج بيانات تجريبية للاختبار

### 3. تسجيل الدخول

- **رابط لوحة التحكم**: `http://localhost/ngo/admin/`
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## البنية التقنية

### الملفات الرئيسية

```
admin/
├── index.php          # الصفحة الرئيسية للوحة التحكم
├── login.php          # صفحة تسجيل الدخول
├── logout.php         # تسجيل الخروج
├── setup.php          # إعداد قاعدة البيانات
├── admin-style.css    # تنسيقات لوحة التحكم
└── admin-script.js    # JavaScript للوحة التحكم
```

### قاعدة البيانات

- **اسم قاعدة البيانات**: `ngo_db`
- **الجداول**:
  - `activities` - النشاطات
  - `news` - الأخبار
  - `beneficiaries` - المستفيدين
  - `donations` - التبرعات
  - `volunteers` - المتطوعين
  - `settings` - الإعدادات

## الحماية والأمان

### مميزات الحماية الحالية
- جلسات PHP لإدارة تسجيل الدخول
- فحص صلاحيات الوصول
- حماية من SQL Injection باستخدام PDO

### تحسينات مقترحة للإنتاج
- استخدام تشفير قوي لكلمات المرور (bcrypt)
- إضافة التحقق بخطوتين
- تحديد أدوار المستخدمين
- تسجيل العمليات (Audit Log)
- استخدام HTTPS

## التخصيص والتوسيع

### إضافة ميزات جديدة
1. أضف القسم الجديد في `index.php`
2. أنشئ الجداول المطلوبة في قاعدة البيانات
3. أضف التنسيقات في `admin-style.css`
4. أضف الوظائف في `admin-script.js`

### تخصيص التصميم
- جميع الألوان معرفة في متغيرات CSS
- تصميم متجاوب يعمل على جميع الأجهزة
- استخدام خطوط Cairo للنصوص العربية

## الدعم والصيانة

### النسخ الاحتياطي
- انسخ ملفات المشروع
- انسخ قاعدة البيانات باستخدام phpMyAdmin

### التحديثات
- احتفظ بنسخة احتياطية قبل أي تحديث
- اختبر التحديثات في بيئة التطوير أولاً

## المساهمة في التطوير

لتحسين لوحة التحكم:
1. أنشئ فرع جديد للميزة
2. اختبر التغييرات محلياً
3. تأكد من توافق الكود مع المعايير
4. أرسل طلب دمج (Pull Request)

## الترخيص

هذا المشروع مخصص لرابطة أعن بإحسان الخيرية ويمكن استخدامه وتطويره حسب الحاجة.

---

**ملاحظة**: هذه النسخة الأولى من لوحة التحكم. يمكن إضافة المزيد من الميزات والتحسينات حسب احتياجات المنظمة.
