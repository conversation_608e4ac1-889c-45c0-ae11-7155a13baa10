<?php
// admin_header.php - المكونات المشتركة لصفحات الإدارة

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// Include activities manager
require_once 'activities_manager.php';

// Initialize activities file
initializeActivitiesFile();

// Get real statistics from activities
$activitiesStats = getActivitiesStats();
$stats = [
    'total_activities' => $activitiesStats['total']
];

function renderAdminHeader($pageTitle = 'لوحة التحكم') {
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - رابطة أعن بإحسان الخيرية</title>
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="admin-style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="admin-body">
    <div class="admin-wrapper">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <a href="../index.php" class="logo-link">
                        <img src="../logo.png" alt="شعار رابطة أعن بإحسان الخيرية" class="sidebar-logo">
                    </a>
                </div>
                <h2><i class="fas fa-cogs"></i> لوحة التحكم</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="dashboard.php" class="nav-item"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                    <li><a href="activities.php" class="nav-item"><i class="fas fa-calendar-alt"></i> إدارة النشاطات</a></li>
                    <li><a href="../index.php" class="nav-item"><i class="fas fa-home"></i> العودة للموقع</a></li>
                    <li><a href="logout.php" class="nav-item logout"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <h1><?php echo $pageTitle; ?></h1>
                <div class="admin-user-info">
                    <span>مرحباً، <?php echo $_SESSION['admin_full_name'] ?? 'المدير'; ?></span>
                    <time><?php echo date('Y-m-d H:i'); ?></time>
                </div>
            </header>
<?php
}

function renderAdminFooter() {
?>
        </main>
    </div>

    <script src="admin-script.js"></script>
</body>
</html>
<?php
}

function showAlert($message, $type = 'success') {
    return "
    <div class=\"alert alert-{$type}\">
        <i class=\"fas fa-" . ($type === 'success' ? 'check-circle' : 'exclamation-circle') . "\"></i>
        " . htmlspecialchars($message) . "
    </div>";
}
?>
