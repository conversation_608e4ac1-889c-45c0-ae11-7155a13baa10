<?php
/**
 * Activities Management Functions - Database Version
 */

require_once 'db_activities.php';
require_once __DIR__ . '/config.php';

/**
 * Get all activities from database with JSON fallback
 */
function getActivities() {
    $activities = getActivitiesFromDB();

    // If database fails, try JSON file fallback
    if (empty($activities)) {
        $activities = getActivitiesFromJSON();
    }

    return $activities;
}

/**
 * Add new activity to database with JSON fallback
 */
function addActivity($data) {
    $result = addActivityToDB($data);

    // If database fails, try adding to JSON file as fallback
    if (!$result) {
        $result = addActivityToJSON($data);
    }

    return $result;
}

/**
 * Update activity in database
 */
function updateActivity($id, $data) {
    return updateActivityInDB($id, $data);
}

/**
 * Delete activity from database
 */
function deleteActivity($id) {
    return deleteActivityFromDB($id);
}

/**
 * Get activity by ID from database
 */
function getActivityById($id) {
    return getActivityByIdFromDB($id);
}

/**
 * Get activities by status from database
 */
function getActivitiesByStatus($status) {
    $pdo = getDatabaseConnection();
    if (!$pdo) return [];
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM activities WHERE status = :status ORDER BY activity_date DESC");
        $stmt->execute([':status' => $status]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get activities by status: " . $e->getMessage());
        return [];
    }
}

/**
 * Get featured activities from database with JSON fallback
 */
function getFeaturedActivities() {
    $pdo = getDatabaseConnection();
    if ($pdo) {
        try {
            $stmt = $pdo->prepare("SELECT * FROM activities WHERE featured = 1 ORDER BY activity_date DESC");
            $stmt->execute();
            $activities = $stmt->fetchAll();
            if (!empty($activities)) {
                return $activities;
            }
        } catch (PDOException $e) {
            error_log("Failed to get featured activities: " . $e->getMessage());
        }
    }

    // Fallback to JSON file
    $allActivities = getActivitiesFromJSON();
    return array_filter($allActivities, function($activity) {
        return isset($activity['featured']) && $activity['featured'] === true;
    });
}

/**
 * Get activities statistics from database with JSON fallback
 */
function getActivitiesStats() {
    $stats = getActivitiesStatsFromDB();

    // If database fails, calculate from JSON file
    if ($stats['total'] == 0) {
        $stats = calculateStatsFromJSON();
    }

    return $stats;
}

/**
 * Upload activity image
 */
function uploadActivityImage($file) {
    $uploadDir = __DIR__ . '/../uploads/activities/';
    
    // Create upload directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    // Check file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'activity_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return 'uploads/activities/' . $filename;
    }
    
    return false;
}

// Legacy file-based functions for backward compatibility (if needed)
function initializeActivitiesFile() {
    // No longer needed - database initialization is handled in db_activities.php
    return true;
}

function getActivitiesFile() {
    // No longer used
    return null;
}

function saveActivities($activities) {
    // No longer used - activities are saved directly to database
    return true;
}

/**
 * Get activities from JSON file as fallback
 */
function getActivitiesFromJSON() {
    // Try multiple locations for JSON file
    $jsonFiles = [
        __DIR__ . '/../database/data/activities.json',
        '/tmp/ngo_activities.json',
        __DIR__ . '/../activities.json'
    ];

    $jsonFile = null;
    foreach ($jsonFiles as $file) {
        if (file_exists($file) && is_readable($file)) {
            $jsonFile = $file;
            break;
        }
    }

    // If no file found, try to create one in writable location
    if (!$jsonFile) {
        $writableLocations = [
            __DIR__ . '/../database/data/activities.json',
            '/tmp/ngo_activities.json',
            __DIR__ . '/../activities.json'
        ];

        foreach ($writableLocations as $location) {
            $dir = dirname($location);
            if (is_writable($dir) || is_writable('/tmp')) {
                if ($location === '/tmp/ngo_activities.json' || is_writable($dir)) {
                    file_put_contents($location, '[]');
                    $jsonFile = $location;
                    break;
                }
            }
        }
    }

    if (!$jsonFile) {
        return [];
    }

    if (!file_exists($jsonFile)) {
        return [];
    }

    $jsonContent = file_get_contents($jsonFile);
    if ($jsonContent === false) {
        return [];
    }

    $activities = json_decode($jsonContent, true);
    if (!is_array($activities)) {
        return [];
    }

    // Sort by activity_date DESC
    usort($activities, function($a, $b) {
        return strtotime($b['activity_date']) - strtotime($a['activity_date']);
    });

    return $activities;
}

/**
 * Calculate statistics from JSON data
 */
function calculateStatsFromJSON() {
    $activities = getActivitiesFromJSON();

    if (empty($activities)) {
        return [
            'total' => 0,
            'completed' => 0,
            'pending' => 0,
            'in_progress' => 0,
            'cancelled' => 0,
            'this_month' => 0,
            'total_beneficiaries' => 0,
            'total_budget' => 0
        ];
    }

    $stats = [
        'total' => count($activities),
        'completed' => 0,
        'pending' => 0,
        'in_progress' => 0,
        'cancelled' => 0,
        'this_month' => 0,
        'total_beneficiaries' => 0,
        'total_budget' => 0
    ];

    $currentMonth = date('Y-m');

    foreach ($activities as $activity) {
        // Count by status
        $status = $activity['status'] ?? 'pending';
        if (isset($stats[$status])) {
            $stats[$status]++;
        }

        // Count this month activities
        if (isset($activity['activity_date']) &&
            date('Y-m', strtotime($activity['activity_date'])) === $currentMonth) {
            $stats['this_month']++;
        }

        // Sum beneficiaries and budget
        $stats['total_beneficiaries'] += (int)($activity['beneficiaries_count'] ?? 0);
        $stats['total_budget'] += (float)($activity['budget'] ?? 0);
    }

    return $stats;
}

/**
 * Add activity to JSON file as fallback
 */
function addActivityToJSON($data) {
    // Try multiple locations for JSON file
    $jsonFiles = [
        __DIR__ . '/../database/data/activities.json',
        '/tmp/ngo_activities.json',
        __DIR__ . '/../activities.json'
    ];

    $jsonFile = null;
    foreach ($jsonFiles as $file) {
        if (file_exists($file) && is_writable($file)) {
            $jsonFile = $file;
            break;
        }
    }

    // If no writable file found, try to create one
    if (!$jsonFile) {
        $writableLocations = [
            __DIR__ . '/../database/data/activities.json',
            '/tmp/ngo_activities.json',
            __DIR__ . '/../activities.json'
        ];

        foreach ($writableLocations as $location) {
            $dir = dirname($location);
            if ($location === '/tmp/ngo_activities.json' || is_writable($dir)) {
                $jsonFile = $location;
                break;
            }
        }
    }

    if (!$jsonFile) {
        $_SESSION['db_error'] = "لا يمكن العثور على مجلد قابل للكتابة لحفظ البيانات";
        return false;
    }

    // Get existing activities
    $activities = getActivitiesFromJSON();

    // Check for duplicates
    foreach ($activities as $activity) {
        if ($activity['title'] === $data['title'] && $activity['activity_date'] === $data['activity_date']) {
            $_SESSION['db_error'] = "يوجد نشاط بنفس العنوان والتاريخ مسبقاً في ملف البيانات";
            return false;
        }
    }

    // Generate new ID
    $maxId = 0;
    foreach ($activities as $activity) {
        if (isset($activity['id']) && $activity['id'] > $maxId) {
            $maxId = $activity['id'];
        }
    }
    $newId = $maxId + 1;

    // Prepare new activity data
    $newActivity = [
        'id' => $newId,
        'title' => $data['title'],
        'description' => $data['description'],
        'content' => $data['content'] ?? $data['description'],
        'activity_date' => $data['activity_date'],
        'location' => $data['location'] ?? '',
        'beneficiaries_count' => (int)($data['beneficiaries_count'] ?? 0),
        'budget' => (float)($data['budget'] ?? 0),
        'status' => $data['status'] ?? 'pending',
        'category' => $data['category'] ?? 'general',
        'featured' => $data['featured'] ? true : false,
        'image' => $data['image'] ?? '',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];

    // Add to beginning of array (newest first)
    array_unshift($activities, $newActivity);

    // Save to file
    $jsonContent = json_encode($activities, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    if (file_put_contents($jsonFile, $jsonContent)) {
        return $newId;
    }

    return false;
}
?>
