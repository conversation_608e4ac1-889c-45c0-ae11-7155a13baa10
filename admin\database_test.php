<?php
require_once __DIR__ . '/config.php';
/**
 * NGO Charity Database Test Script
 * Upload this to your website to test MySQL connectivity
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// HTML header
echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>NGO Database Test</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        table, th, td { border: 1px solid #ddd; }
        th, td { padding: 8px; text-align: left; }
    </style>
</head>
<body>
    <h1>NGO Charity Database Test</h1>";

try {
    // Test database connection
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    echo "<p class='success'>✅ Successfully connected to MySQL server</p>";
    echo "<p>MySQL Server Version: " . $conn->server_info . "</p>";
    
    // Test CREATE TABLE
    $createTableSQL = "CREATE TABLE IF NOT EXISTS test_website_connection (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_string VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB";
    
    if ($conn->query($createTableSQL) === TRUE) {
        echo "<p class='success'>✅ Test table created successfully</p>";
    } else {
        throw new Exception("Error creating table: " . $conn->error);
    }
    
    // Test INSERT
    $testString = "PHP Connection Test " . date('Y-m-d H:i:s');
    $insertSQL = "INSERT INTO test_website_connection (test_string) VALUES (?)";
    $stmt = $conn->prepare($insertSQL);
    $stmt->bind_param("s", $testString);
    
    if ($stmt->execute()) {
        echo "<p class='success'>✅ Test record inserted successfully</p>";
        $last_id = $conn->insert_id;
    } else {
        throw new Exception("Error inserting record: " . $conn->error);
    }
    
    // Test SELECT
    $selectSQL = "SELECT * FROM test_website_connection ORDER BY created_at DESC LIMIT 5";
    $result = $conn->query($selectSQL);
    
    if ($result->num_rows > 0) {
        echo "<h3>Last 5 test records:</h3>";
        echo "<table>
                <tr>
                    <th>ID</th>
                    <th>Test String</th>
                    <th>Created At</th>
                </tr>";
        
        while($row = $result->fetch_assoc()) {
            echo "<tr>
                    <td>{$row['id']}</td>
                    <td>{$row['test_string']}</td>
                    <td>{$row['created_at']}</td>
                  </tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No test records found</p>";
    }
    
    // Test DELETE (cleanup)
    $deleteSQL = "DELETE FROM test_website_connection WHERE id = ?";
    $stmt = $conn->prepare($deleteSQL);
    $stmt->bind_param("i", $last_id);
    
    if ($stmt->execute()) {
        echo "<p class='success'>✅ Test record cleanup successful</p>";
    } else {
        echo "<p class='error'>⚠️ Could not clean up test record: " . $conn->error . "</p>";
    }
    
    // Close connection
    $conn->close();
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

// Display PHP info for debugging
echo "<h3>PHP Information:</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>MySQLi Extension: " . (function_exists('mysqli_connect') ? 'Enabled' : 'Disabled') . "</p>";

echo "</body>
</html>";
?>