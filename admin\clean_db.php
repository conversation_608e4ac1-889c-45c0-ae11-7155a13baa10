<?php
/**
 * Clean Database Script - Remove Duplicates and Fix Issues
 */

echo "<h2>تنظيف قاعدة البيانات</h2>";

try {
    $mysqli = new mysqli('localhost', 'root', '', 'ngo_charity');
    
    if ($mysqli->connect_error) {
        throw new Exception("فشل الاتصال: " . $mysqli->connect_error);
    }
    
    echo "✅ تم الاتصال بقاعدة البيانات<br>";
    
    // Check for duplicates
    echo "<h3>فحص النشاطات المكررة:</h3>";
    $result = $mysqli->query("
        SELECT title, COUNT(*) as count 
        FROM activities 
        GROUP BY title 
        HAVING COUNT(*) > 1
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "تم العثور على نشاطات مكررة:<br>";
        while ($row = $result->fetch_assoc()) {
            echo "• {$row['title']} - {$row['count']} مرات<br>";
        }
        
        // Remove duplicates (keep only the latest one)
        echo "<br>🧹 إزالة المكررات...<br>";
        $mysqli->query("
            DELETE a1 FROM activities a1
            INNER JOIN activities a2 
            WHERE a1.id < a2.id 
            AND a1.title = a2.title 
            AND a1.activity_date = a2.activity_date
        ");
        
        echo "✅ تم حذف النشاطات المكررة<br>";
    } else {
        echo "✅ لا توجد نشاطات مكررة<br>";
    }
    
    // Get current count
    $result = $mysqli->query("SELECT COUNT(*) as count FROM activities");
    $row = $result->fetch_assoc();
    echo "<br><strong>العدد الحالي للنشاطات: {$row['count']}</strong><br>";
    
    // Show all activities
    echo "<h3>قائمة النشاطات الحالية:</h3>";
    $result = $mysqli->query("SELECT id, title, created_at FROM activities ORDER BY created_at DESC");
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>العنوان</th><th>تاريخ الإنشاء</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>" . htmlspecialchars($row['title']) . "</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $mysqli->close();
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}

echo "<br><h3>روابط:</h3>";
echo "• <a href='activities.php'>العودة لإدارة النشاطات</a><br>";
echo "• <a href='../activities.php'>عرض النشاطات في الموقع</a><br>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background-color: #f0f0f0; }
</style>
