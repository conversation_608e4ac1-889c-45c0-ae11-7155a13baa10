<?php
/**
 * إعدادات قاعدة البيانات للاستضافة
 * يجب تعديل هذه القيم حسب إعدادات الاستضافة الخاصة بك
 */

// إعدادات قاعدة البيانات للاستضافة
define('HOSTING_DB_HOST', 'localhost'); // عادة localhost أو عنوان محدد من الاستضافة
define('HOSTING_DB_USER', 'cpanel_username_dbname'); // اسم المستخدم من cPanel
define('HOSTING_DB_PASS', 'your_database_password'); // كلمة مرور قاعدة البيانات
define('HOSTING_DB_NAME', 'cpanel_username_dbname'); // اسم قاعدة البيانات

/**
 * ملاحظات مهمة للاستضافة:
 * 
 * 1. في معظم الاستضافات المشتركة، اسم قاعدة البيانات يكون:
 *    cpanel_username_database_name
 *    مثال: user123_ngo_charity
 * 
 * 2. اسم المستخدم يكون عادة:
 *    cpanel_username_database_user
 *    مثال: user123_ngo_user
 * 
 * 3. يجب إنشاء قاعدة البيانات والمستخدم من cPanel أولاً
 * 
 * 4. تأكد من ربط المستخدم بقاعدة البيانات وإعطاء جميع الصلاحيات
 */

// إعدادات إضافية للاستضافة
define('HOSTING_ENVIRONMENT', true);
define('DEBUG_MODE', false); // إيقاف وضع التشخيص في الاستضافة
define('ERROR_REPORTING_LEVEL', E_ERROR); // عرض الأخطاء الحرجة فقط

// مسارات الملفات في الاستضافة
define('UPLOAD_PATH', '/home/<USER>/public_html/uploads/'); // مسار رفع الملفات
define('JSON_DATA_PATH', '/home/<USER>/public_html/database/data/'); // مسار ملفات JSON

/**
 * دالة للحصول على إعدادات قاعدة البيانات حسب البيئة
 */
function getDatabaseConfig() {
    if (defined('HOSTING_ENVIRONMENT') && HOSTING_ENVIRONMENT) {
        return [
            'host' => HOSTING_DB_HOST,
            'user' => HOSTING_DB_USER,
            'pass' => HOSTING_DB_PASS,
            'name' => HOSTING_DB_NAME
        ];
    } else {
        return [
            'host' => 'localhost',
            'user' => 'ngo',
            'pass' => 'Ngo1234@#charity',
            'name' => 'ngo_charity'
        ];
    }
}
?>
