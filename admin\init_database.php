<?php
/**
 * Database Initialization Script
 * Run this file once to create the database and tables
 */

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS ngo_charity DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Database 'ngo_charity' created successfully.<br>";
    
    // Use the database
    $pdo->exec("USE ngo_charity");
    
    // Create activities table
    $createTable = "
    CREATE TABLE IF NOT EXISTS activities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        content TEXT,
        activity_date DATE,
        location VARCHAR(255),
        beneficiaries_count INT DEFAULT 0,
        budget DECIMAL(12,2) DEFAULT 0.00,
        status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
        category VARCHAR(50) DEFAULT 'general',
        featured BOOLEAN DEFAULT FALSE,
        image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTable);
    echo "✅ Table 'activities' created successfully.<br>";
    
    // Check if table is empty and insert sample data
    $stmt = $pdo->query("SELECT COUNT(*) FROM activities");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Insert sample data
        $insertData = "
        INSERT INTO activities (title, description, content, activity_date, location, beneficiaries_count, budget, status, category, featured, image) VALUES
        ('توزيع المساعدات الغذائية', 'توزيع الطرود الغذائية على الأسر المحتاجة في الموصل', 'نقوم بتوزيع الطرود الغذائية التي تحتوي على مواد أساسية مثل الأرز والسكر والزيت والدقيق وغيرها من المواد الضرورية للأسر المحتاجة والفقيرة في مدينة الموصل وضواحيها.', '2024-01-15', 'الموصل - أحياء مختلفة', 150, 25000.00, 'completed', 'food', TRUE, ''),
        ('حملة التطعيم المجاني', 'حملة تطعيم مجانية للأطفال في المناطق النائية', 'تنظيم حملة تطعيم مجانية بالتعاون مع وزارة الصحة لتطعيم الأطفال ضد الأمراض المعدية في المناطق النائية والمحرومة.', '2024-02-20', 'قرى وأطراف الموصل', 300, 15000.00, 'completed', 'health', TRUE, ''),
        ('كسوة الشتاء', 'توزيع الملابس الشتوية والبطانيات على المحتاجين', 'مع دخول فصل الشتاء، نقوم بتوزيع الملابس الشتوية والبطانيات والأحذية على الأسر الفقيرة والأيتام لمساعدتهم في مواجهة برد الشتاء.', '2024-01-05', 'مراكز الإيواء والأحياء الفقيرة', 200, 18000.00, 'completed', 'social', FALSE, ''),
        ('مشروع كفالة الأيتام', 'برنامج شهري لكفالة ومتابعة الأطفال الأيتام', 'برنامج شامل لكفالة الأيتام يشمل الدعم المالي الشهري، المتابعة التعليمية، الرعاية الصحية، والأنشطة الترفيهية والتربوية.', '2024-01-01', 'مختلف أنحاء الموصل', 75, 45000.00, 'in_progress', 'social', TRUE, ''),
        ('دورة تعليم الكمبيوتر', 'دورات مجانية لتعليم أساسيات الحاسوب', 'دورات تدريبية مجانية لتعليم الشباب والشابات أساسيات استخدام الحاسوب والإنترنت لمساعدتهم في إيجاد فرص عمل أفضل.', '2024-03-10', 'مقر الرابطة', 40, 8000.00, 'pending', 'education', FALSE, ''),
        ('إفطار الصائم', 'توزيع وجبات الإفطار في رمضان', 'توزيع وجبات الإفطار يومياً خلال شهر رمضان المبارك على الفقراء والمحتاجين في مختلف أنحاء المدينة.', '2024-03-15', 'عدة مواقع في الموصل', 500, 35000.00, 'pending', 'food', TRUE, '')
        ";
        
        $pdo->exec($insertData);
        echo "✅ Sample data inserted successfully.<br>";
        echo "📊 Inserted 6 sample activities.<br>";
    } else {
        echo "ℹ️ Table already contains {$count} records. No sample data inserted.<br>";
    }
    
    echo "<br><strong>🎉 Database initialization completed successfully!</strong><br>";
    echo "<br>You can now:<br>";
    echo "• View activities at: <a href='../activities.php'>../activities.php</a><br>";
    echo "• Access admin panel at: <a href='activities.php'>activities.php</a><br>";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Please make sure:<br>";
    echo "• XAMPP MySQL service is running<br>";
    echo "• MySQL credentials are correct (default: root with no password)<br>";
}
?>
