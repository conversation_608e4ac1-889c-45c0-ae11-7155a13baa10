// Admin Panel JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize admin panel
    initializeAdminPanel();
});

// Also try to initialize when window loads (fallback)
window.addEventListener('load', function() {
    if (!window.adminInitialized) {
        initializeAdminPanel();
    }
});

function initializeAdminPanel() {
    // Mark as initialized
    window.adminInitialized = true;
    
    // Set up navigation
    setupNavigation();
    
    // Set up form toggles
    setupFormToggles();
    
    // Set up table actions
    setupTableActions();
    
    // Set up mobile menu if needed
    setupMobileMenu();
    
    // Set up image previews
    setupImagePreviews();
}

// Navigation functionality
function setupNavigation() {
    const navItems = document.querySelectorAll('.sidebar-nav .nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Skip if it's a logout or external link
            if (this.classList.contains('logout') || 
                href.includes('../index.php') || 
                href.includes('logout.php') ||
                !href.startsWith('#')) {
                return; // Allow default behavior for external links
            }
            
            e.preventDefault();
            
            // Get target section
            const target = href.substring(1);
            
            // Update active nav item
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // Show target section
            showSection(target);
        });
    });
}

// Show specific section
function showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.admin-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Update navigation
    const navItems = document.querySelectorAll('.sidebar-nav .nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
        if (item.getAttribute('href') === '#' + sectionId) {
            item.classList.add('active');
        }
    });
}

// Form toggle functionality
function setupFormToggles() {
    window.toggleForm = function(formId) {
        const form = document.getElementById(formId);
        if (form) {
            if (form.style.display === 'none' || !form.style.display) {
                form.style.display = 'block';
                form.scrollIntoView({ behavior: 'smooth' });
            } else {
                form.style.display = 'none';
            }
        }
    };
}

// Table actions setup
function setupTableActions() {
    // Edit button functionality
    const editButtons = document.querySelectorAll('.btn-icon.edit');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const title = row.cells[0].textContent;
            
            // Edit functionality is handled by page-specific functions
            console.log('تعديل:', title);
        });
    });
    
    // Delete button functionality
    const deleteButtons = document.querySelectorAll('.btn-icon.delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const title = row.cells[0].textContent;
            
            // Delete confirmation is handled by page-specific functions
            console.log('حذف:', title);
        });
    });
}

// Mobile menu setup
function setupMobileMenu() {
    // Create mobile menu toggle if needed
    if (window.innerWidth <= 768) {
        createMobileMenuToggle();
    }
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            createMobileMenuToggle();
        } else {
            removeMobileMenuToggle();
        }
    });
}

function createMobileMenuToggle() {
    // Check if toggle already exists
    if (document.querySelector('.mobile-menu-toggle')) {
        return;
    }
    
    const toggle = document.createElement('button');
    toggle.className = 'mobile-menu-toggle';
    toggle.innerHTML = '<i class="fas fa-bars"></i>';
    toggle.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1001;
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 0.75rem;
        border-radius: 6px;
        cursor: pointer;
    `;
    
    toggle.addEventListener('click', function() {
        const sidebar = document.querySelector('.admin-sidebar');
        sidebar.classList.toggle('mobile-open');
    });
    
    document.body.appendChild(toggle);
}

function removeMobileMenuToggle() {
    const toggle = document.querySelector('.mobile-menu-toggle');
    if (toggle) {
        toggle.remove();
    }
    
    // Remove mobile-open class from sidebar
    const sidebar = document.querySelector('.admin-sidebar');
    sidebar.classList.remove('mobile-open');
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create new notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 2rem;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        padding: 1rem 1.5rem;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 2000;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        border-right: 4px solid ${getNotificationColor(type)};
        animation: slideDown 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

function getNotificationColor(type) {
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    return colors[type] || '#17a2b8';
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#dc3545';
            isValid = false;
        } else {
            field.style.borderColor = '#dee2e6';
        }
    });
    
    return isValid;
}

// Image preview for file uploads
function setupImagePreviews() {
    const fileInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Create or update preview
                    let preview = input.parentElement.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('div');
                        preview.className = 'image-preview';
                        preview.style.cssText = `
                            margin-top: 0.5rem;
                            max-width: 200px;
                        `;
                        input.parentElement.appendChild(preview);
                    }
                    
                    preview.innerHTML = `
                        <img src="${e.target.result}" style="width: 100%; border-radius: 6px; border: 1px solid #dee2e6;">
                        <button type="button" onclick="this.parentElement.remove(); this.parentElement.previousElementSibling.value=''" 
                                style="margin-top: 0.5rem; padding: 0.25rem 0.5rem; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">
                            إزالة الصورة
                        </button>
                    `;
                };
                reader.readAsDataURL(file);
            }
        });
    });
}

// Initialize image previews when DOM is loaded
document.addEventListener('DOMContentLoaded', setupImagePreviews);

// Add CSS animation keyframes
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }
    
    .notification-close {
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        color: #6c757d;
    }
    
    .notification-close:hover {
        color: #495057;
    }
`;
document.head.appendChild(style);
