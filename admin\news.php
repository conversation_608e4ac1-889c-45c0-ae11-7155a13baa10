<?php
session_start();
require_once 'admin_header.php';
require_once 'db_news.php';

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_news') {
        $newsData = [
            'title' => $_POST['news_title'] ?? '',
            'content' => $_POST['news_content'] ?? '',
            'summary' => $_POST['news_summary'] ?? '',
            'news_date' => $_POST['news_date'] ?? date('Y-m-d'),
            'featured' => isset($_POST['featured']) ? true : false,
            'status' => $_POST['news_status'] ?? 'published'
        ];
        
        // Handle image upload
        if (isset($_FILES['news_image']) && $_FILES['news_image']['error'] === UPLOAD_ERR_OK) {
            $imagePath = uploadNewsImage($_FILES['news_image']);
            if ($imagePath) {
                $newsData['image'] = $imagePath;
            }
        }
        
        // Add news to database
        $result = addNews($newsData);
        if ($result) {
            $success_message = "تم إضافة الخبر بنجاح";
        } else {
            $error_message = "فشل في إضافة الخبر";
        }
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'delete_news') {
        $newsId = $_POST['news_id'] ?? 0;
        if ($newsId && deleteNews($newsId)) {
            $success_message = "تم حذف الخبر بنجاح";
        } else {
            $error_message = "فشل في حذف الخبر";
        }
    }
}

// Get news for display
$news = getNews();

renderAdminHeader('إدارة الأخبار');

// News management functions
function addNews($data) {
    return addNewsToDB($data);
}

function getNews() {
    return getNewsFromDB();
}

function deleteNews($id) {
    return deleteNewsFromDB($id);
}

function uploadNewsImage($file) {
    $uploadDir = __DIR__ . '/../uploads/news/';
    
    // Create upload directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    // Check file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'news_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return 'uploads/news/' . $filename;
    }
    
    return false;
}
?>

            <?php if (!empty($success_message)): ?>
                <?php echo showAlert($success_message, 'success'); ?>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <?php echo showAlert($error_message, 'error'); ?>
            <?php endif; ?>

            <!-- News Management -->
            <div class="admin-section active">
                <h2><i class="fas fa-newspaper"></i> إدارة الأخبار</h2>
                
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="toggleForm('news-form')">
                        <i class="fas fa-plus"></i> إضافة خبر جديد
                    </button>
                </div>

                <div id="news-form" class="form-container" style="display: none;">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="add_news">
                        
                        <div class="form-group">
                            <label for="news_title">عنوان الخبر *</label>
                            <input type="text" id="news_title" name="news_title" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="news_summary">ملخص الخبر *</label>
                            <textarea id="news_summary" name="news_summary" rows="3" required placeholder="ملخص مختصر للخبر"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="news_content">محتوى الخبر *</label>
                            <textarea id="news_content" name="news_content" rows="8" required placeholder="المحتوى الكامل للخبر"></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="news_date">تاريخ النشر</label>
                                <input type="date" id="news_date" name="news_date" value="<?php echo date('Y-m-d'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="news_status">حالة النشر</label>
                                <select id="news_status" name="news_status">
                                    <option value="published">منشور</option>
                                    <option value="draft">مسودة</option>
                                    <option value="scheduled">مجدول</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="news_image">صورة الخبر</label>
                                <input type="file" id="news_image" name="news_image" accept="image/*">
                                <small>اختياري - يُفضل صور بجودة عالية</small>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="featured"> خبر مميز
                                    <span class="checkmark"></span>
                                </label>
                                <small>سيظهر في المقدمة على الموقع</small>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ الخبر
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="toggleForm('news-form')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>

                <div class="data-table">
                    <table>
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>تاريخ النشر</th>
                                <th>الحالة</th>
                                <th>مميز</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($news)): ?>
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد أخبار مسجلة</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($news as $item): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($item['title']); ?></strong>
                                            <br><small><?php echo htmlspecialchars(substr($item['summary'], 0, 100)) . '...'; ?></small>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($item['news_date'])); ?></td>
                                        <td>
                                            <?php 
                                            $statusClass = '';
                                            $statusText = '';
                                            switch($item['status']) {
                                                case 'published':
                                                    $statusClass = 'completed';
                                                    $statusText = 'منشور';
                                                    break;
                                                case 'draft':
                                                    $statusClass = 'pending';
                                                    $statusText = 'مسودة';
                                                    break;
                                                case 'scheduled':
                                                    $statusClass = 'in-progress';
                                                    $statusText = 'مجدول';
                                                    break;
                                                default:
                                                    $statusClass = 'pending';
                                                    $statusText = $item['status'];
                                            }
                                            ?>
                                            <span class="status <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($item['featured']): ?>
                                                <i class="fas fa-star text-warning" title="خبر مميز"></i>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button class="btn-icon edit" title="تعديل" onclick="editNews(<?php echo $item['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete" title="حذف" onclick="if(confirm('هل أنت متأكد من حذف هذا الخبر؟')) { deleteNews(<?php echo $item['id']; ?>); }">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

<script>
function toggleForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        if (form.style.display === 'none' || form.style.display === '') {
            form.style.display = 'block';
            form.scrollIntoView({ behavior: 'smooth' });
        } else {
            form.style.display = 'none';
        }
    }
}

function deleteNews(newsId) {
    // Create a form to submit the delete request
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';
    
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'delete_news';
    
    const idInput = document.createElement('input');
    idInput.type = 'hidden';
    idInput.name = 'news_id';
    idInput.value = newsId;
    
    form.appendChild(actionInput);
    form.appendChild(idInput);
    
    document.body.appendChild(form);
    form.submit();
}

function editNews(newsId) {
    // Placeholder for edit functionality
    alert('وظيفة التعديل ستتوفر قريباً');
}
</script>

<?php renderAdminFooter(); ?>
