<?php
/**
 * Database Connection Test
 */

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

echo "<h3>1. اختبار اتصال MySQLi</h3>";
try {
    $mysqli = new mysqli('localhost', 'root', '', 'ngo_charity');
    
    if ($mysqli->connect_error) {
        echo "❌ فشل الاتصال: " . $mysqli->connect_error . "<br>";
        
        // Try connecting to MySQL server without database
        echo "<h4>محاولة الاتصال بخادم MySQL...</h4>";
        $mysqli = new mysqli('localhost', 'root', '');
        if ($mysqli->connect_error) {
            echo "❌ فشل الاتصال بخادم MySQL: " . $mysqli->connect_error . "<br>";
        } else {
            echo "✅ نجح الاتصال بخادم MySQL<br>";
            echo "📋 قوائم قواعد البيانات الموجودة:<br>";
            $result = $mysqli->query("SHOW DATABASES");
            while ($row = $result->fetch_array()) {
                echo "• " . $row[0] . "<br>";
            }
        }
    } else {
        echo "✅ نجح الاتصال بقاعدة البيانات ngo_charity<br>";
        
        echo "<h4>فحص الجداول:</h4>";
        $result = $mysqli->query("SHOW TABLES");
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_array()) {
                echo "• " . $row[0] . "<br>";
            }
        } else {
            echo "لا توجد جداول<br>";
        }
        
        // Test activities table specifically
        $result = $mysqli->query("SHOW TABLES LIKE 'activities'");
        if ($result->num_rows > 0) {
            echo "<h4>بنية جدول activities:</h4>";
            $result = $mysqli->query("DESCRIBE activities");
            while ($row = $result->fetch_assoc()) {
                echo "• {$row['Field']} ({$row['Type']})<br>";
            }
            
            $result = $mysqli->query("SELECT COUNT(*) as count FROM activities");
            $row = $result->fetch_assoc();
            echo "<br><strong>عدد النشاطات: {$row['count']}</strong><br>";
        }
    }
    
    $mysqli->close();
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}

echo "<br><h3>2. اختبار اتصال PDO</h3>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=ngo_charity;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ نجح الاتصال بـ PDO<br>";
} catch (PDOException $e) {
    echo "❌ فشل اتصال PDO: " . $e->getMessage() . "<br>";
}

echo "<br><h3>3. روابط مفيدة</h3>";
echo "• <a href='setup_database.php'>إعداد قاعدة البيانات</a><br>";
echo "• <a href='activities.php'>إدارة النشاطات</a><br>";
echo "• <a href='../activities.php'>عرض النشاطات</a><br>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3, h4 { color: #333; }
</style>
