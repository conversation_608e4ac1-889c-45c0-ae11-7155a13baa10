<?php
/**
 * إعدادات الاتصال بقاعدة البيانات
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'ngo');
define('DB_PASS', 'Ngo1234@#charity');
define('DB_NAME', 'ngo_charity');

/**
 * كلاس للاتصال بقاعدة البيانات
 */
class Database {
    private $connection;
    private static $instance = null;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, DB_USERNAME, DB_PASSWORD, $options);
        } catch (PDOException $e) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * تنفيذ استعلام SELECT
     */
    public function select($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في استعلام SELECT: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $this->connection->lastInsertId();
        } catch (PDOException $e) {
            error_log("خطأ في استعلام INSERT: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("خطأ في استعلام UPDATE: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("خطأ في استعلام DELETE: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام عام
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * كلاس لإدارة النشاطات
 */
class ActivityManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * جلب جميع النشاطات
     */
    public function getAllActivities($limit = null, $offset = 0) {
        $sql = "SELECT a.*, ac.name as category_name, ac.color as category_color 
                FROM activities a 
                LEFT JOIN activity_categories ac ON a.category = ac.id 
                ORDER BY a.activity_date DESC";
        
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
            return $this->db->select($sql, ['limit' => $limit, 'offset' => $offset]);
        }
        
        return $this->db->select($sql);
    }
    
    /**
     * جلب النشاطات المميزة
     */
    public function getFeaturedActivities($limit = 3) {
        $sql = "SELECT * FROM featured_activities LIMIT :limit";
        return $this->db->select($sql, ['limit' => $limit]);
    }
    
    /**
     * جلب نشاط محدد بالمعرف
     */
    public function getActivityById($id) {
        $sql = "SELECT a.*, ac.name as category_name, ac.color as category_color 
                FROM activities a 
                LEFT JOIN activity_categories ac ON a.category = ac.id 
                WHERE a.id = :id";
        $result = $this->db->select($sql, ['id' => $id]);
        return $result ? $result[0] : null;
    }
    
    /**
     * إضافة نشاط جديد
     */
    public function addActivity($data) {
        $sql = "INSERT INTO activities (title, description, content, activity_date, location, 
                beneficiaries_count, budget, status, category, featured, image_url) 
                VALUES (:title, :description, :content, :activity_date, :location, 
                :beneficiaries_count, :budget, :status, :category, :featured, :image_url)";
        
        return $this->db->insert($sql, $data);
    }
    
    /**
     * تحديث نشاط
     */
    public function updateActivity($id, $data) {
        $sql = "UPDATE activities SET 
                title = :title, description = :description, content = :content,
                activity_date = :activity_date, location = :location,
                beneficiaries_count = :beneficiaries_count, budget = :budget,
                status = :status, category = :category, featured = :featured,
                image_url = :image_url, updated_at = CURRENT_TIMESTAMP
                WHERE id = :id";
        
        $data['id'] = $id;
        return $this->db->update($sql, $data);
    }
    
    /**
     * حذف نشاط
     */
    public function deleteActivity($id) {
        $sql = "DELETE FROM activities WHERE id = :id";
        return $this->db->delete($sql, ['id' => $id]);
    }
    
    /**
     * جلب إحصائيات النشاطات
     */
    public function getActivityStats() {
        $sql = "SELECT * FROM activity_stats";
        return $this->db->select($sql);
    }
    
    /**
     * جلب النشاطات حسب الفئة
     */
    public function getActivitiesByCategory($category) {
        $sql = "SELECT * FROM activities WHERE category = :category ORDER BY activity_date DESC";
        return $this->db->select($sql, ['category' => $category]);
    }
    
    /**
     * البحث في النشاطات
     */
    public function searchActivities($search_term) {
        $sql = "SELECT * FROM activities 
                WHERE title LIKE :search OR description LIKE :search 
                ORDER BY activity_date DESC";
        $search_param = '%' . $search_term . '%';
        return $this->db->select($sql, ['search' => $search_param]);
    }
}

/**
 * كلاس لإدارة فئات النشاطات
 */
class CategoryManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * جلب جميع الفئات
     */
    public function getAllCategories() {
        $sql = "SELECT * FROM activity_categories ORDER BY name";
        return $this->db->select($sql);
    }
    
    /**
     * جلب فئة محددة
     */
    public function getCategoryById($id) {
        $sql = "SELECT * FROM activity_categories WHERE id = :id";
        $result = $this->db->select($sql, ['id' => $id]);
        return $result ? $result[0] : null;
    }
}

/**
 * دالة مساعدة لتنسيق التاريخ
 */
function formatArabicDate($date) {
    $months = [
        '01' => 'يناير', '02' => 'فبراير', '03' => 'مارس', '04' => 'أبريل',
        '05' => 'مايو', '06' => 'يونيو', '07' => 'يوليو', '08' => 'أغسطس',
        '09' => 'سبتمبر', '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
    ];
    
    $date_parts = explode('-', $date);
    $year = $date_parts[0];
    $month = $months[$date_parts[1]];
    $day = $date_parts[2];
    
    return "$day $month $year";
}

/**
 * دالة مساعدة لتنسيق المبالغ
 */
function formatCurrency($amount) {
    return number_format($amount, 0, '.', ',') . ' د.ع';
}

/**
 * دالة مساعدة للحصول على لون الحالة
 */
function getStatusColor($status) {
    $colors = [
        'planned' => '#ffc107',
        'ongoing' => '#17a2b8',
        'completed' => '#28a745',
        'cancelled' => '#dc3545'
    ];
    
    return isset($colors[$status]) ? $colors[$status] : '#6c757d';
}

/**
 * دالة مساعدة للحصول على نص الحالة
 */
function getStatusText($status) {
    $statuses = [
        'planned' => 'مخطط',
        'ongoing' => 'جاري',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي'
    ];
    
    return isset($statuses[$status]) ? $statuses[$status] : 'غير محدد';
}
?>
