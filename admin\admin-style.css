/* Admin Panel Specific Styles */

.admin-body {
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    font-family: 'Cairo', Arial, sans-serif;
}

.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.admin-sidebar {
    width: 280px;
    background: linear-gradient(180deg, var(--primary-color), #1e3d72);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.admin-logo {
    margin-bottom: 1rem;
}

.admin-logo .logo-link {
    display: inline-block;
    text-decoration: none;
    transition: all 0.3s ease;
}

.admin-logo .logo-link:hover {
    opacity: 0.8;
    transform: scale(1.05);
}

.sidebar-logo {
    width: 50px;
    height: 50px;
    object-fit: contain;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.sidebar-logo:hover {
    transform: scale(1.1);
    border-color: rgba(255,255,255,0.4);
}

.admin-logo .logo-link:hover .sidebar-logo {
    transform: scale(1.1) rotate(10deg);
    border-color: rgba(255,255,255,0.6);
}

.sidebar-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
}

.sidebar-nav .nav-item {
    display: block;
    padding: 1rem 1.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.sidebar-nav .nav-item:hover,
.sidebar-nav .nav-item.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-right-color: #fff;
    transform: translateX(-5px);
}

.sidebar-nav .nav-item.logout {
    margin-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    color: #ff6b6b;
}

.sidebar-nav .nav-item.logout:hover {
    background-color: rgba(255,107,107,0.1);
    border-right-color: #ff6b6b;
}

.sidebar-nav .nav-item i {
    margin-left: 0.5rem;
    width: 20px;
    text-align: center;
}

/* Main Content Area */
.admin-main {
    flex: 1;
    margin-right: 280px;
    padding: 0;
    background-color: #f8f9fa;
}

.admin-header {
    background: white;
    padding: 1.5rem 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.admin-header h1 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 600;
}

.admin-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Admin Sections */
.admin-section {
    display: none;
    padding: 2rem;
}

.admin-section.active {
    display: block;
}

.admin-section h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-size: 1.6rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
}

.stat-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Dashboard Widgets */
.dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.widget {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.widget h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.quick-stats {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.quick-stat {
    text-align: center;
    flex: 1;
}

.quick-stat .number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.quick-stat .label {
    font-size: 0.8rem;
    color: #6c757d;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Alerts */
.alert {
    padding: 1rem 1.5rem;
    margin: 1rem 2rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #1e3d72);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(44, 90, 160, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--secondary-color), #1e7e34);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-icon {
    padding: 0.5rem;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.btn-icon.edit {
    color: var(--primary-color);
}

.btn-icon.edit:hover {
    background-color: rgba(44, 90, 160, 0.1);
}

.btn-icon.delete {
    color: var(--danger-color);
}

.btn-icon.delete:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

/* Forms */
.section-actions {
    margin-bottom: 1.5rem;
}

.form-container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

/* Data Tables */
.data-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--primary-color);
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status.completed {
    background-color: #d4edda;
    color: #155724;
}

.status.pending {
    background-color: #fff3cd;
    color: #856404;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.setting-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.setting-card h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .admin-sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-right: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .quick-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .admin-header {
        padding: 1rem;
    }
    
    .admin-section {
        padding: 1rem;
    }
}

/* Additional styles for improved forms */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-row .form-group {
    margin-bottom: 0;
}

/* Status badges */
.status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status.completed {
    background: #d4edda;
    color: #155724;
}

.status.in-progress {
    background: #fff3cd;
    color: #856404;
}

.status.pending {
    background: #cce5ff;
    color: #004085;
}

.status.cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Table improvements */
.data-table table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    text-align: center;
}

.data-table table td {
    vertical-align: middle;
    padding: 0.75rem;
}

.text-center {
    text-align: center;
}

.text-muted {
    color: #6c757d;
}

.text-warning {
    color: #ffc107;
}

/* Alert styles */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Animation for section transitions */
.admin-section {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom Delete Dialog Styles */
.delete-dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 9999;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.delete-dialog-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 450px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease-out;
    overflow: hidden;
}

.delete-dialog-header {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 2rem 1.5rem;
    text-align: center;
    position: relative;
    box-shadow: 0 2px 10px rgba(220, 53, 69, 0.3);
}

.delete-dialog-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.15"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></svg>');
    pointer-events: none;
}

.delete-dialog-header i {
    font-size: 4rem;
    margin-bottom: 0.75rem;
    display: block;
    animation: dangerPulse 2s infinite;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    position: relative;
    z-index: 1;
}

.delete-dialog-header h3 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.delete-dialog-body {
    padding: 2.5rem 2rem;
    text-align: center;
    color: #333;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
}

.delete-dialog-body p {
    margin: 0.75rem 0;
    font-size: 1.15rem;
    line-height: 1.6;
}

.delete-dialog-body strong {
    display: block;
    margin: 1.5rem 0;
    padding: 1rem 1.25rem;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid #ffb347;
    border-radius: 12px;
    color: #856404;
    font-size: 1.25rem;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(255, 179, 71, 0.3);
    position: relative;
    overflow: hidden;
}

.delete-dialog-body strong::before {
    content: '📋';
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
}

.delete-dialog-body strong::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 3s infinite;
}

.warning-text {
    color: #dc3545 !important;
    font-weight: 700;
    font-size: 1rem !important;
    margin-top: 1.5rem !important;
    padding: 0.75rem;
    background: rgba(220, 53, 69, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(220, 53, 69, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.warning-text::before {
    content: '⚠️';
    font-size: 1.2rem;
}

.delete-dialog-footer {
    padding: 2rem 1.5rem;
    background: linear-gradient(180deg, #f8f9fa, #e9ecef);
    display: flex;
    gap: 1.25rem;
    justify-content: center;
    border-top: 2px solid #dee2e6;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.delete-dialog-footer .btn {
    flex: 1;
    max-width: 160px;
    padding: 1rem 1.25rem;
    font-weight: 700;
    font-size: 1rem;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.delete-dialog-footer .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.delete-dialog-footer .btn:hover::before {
    left: 100%;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    position: relative;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.5);
}

.btn-danger:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.5);
}

.btn-secondary:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

/* Animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes dangerPulse {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
    }
    25% {
        transform: scale(1.1);
        filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
    }
    50% {
        transform: scale(1.05);
        filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.4));
    }
    75% {
        transform: scale(1.15);
        filter: drop-shadow(0 0 25px rgba(255, 255, 255, 0.6));
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes fadeOut {
    to {
        opacity: 0;
        transform: translateY(-20px) scale(0.9);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Additional animations for enhanced UX */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(300px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(300px);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive design for dialog */
@media (max-width: 768px) {
    .delete-dialog-content {
        margin: 1rem;
        width: calc(100% - 2rem);
    }
    
    .delete-dialog-footer {
        flex-direction: column;
    }
    
    .delete-dialog-footer .btn {
        max-width: none;
    }
}
