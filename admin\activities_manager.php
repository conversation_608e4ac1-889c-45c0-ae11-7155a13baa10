<?php
/**
 * Activities Management Functions - Database Version
 */

require_once 'db_activities.php';
require_once __DIR__ . '/config.php';

/**
 * Get all activities from database
 */
function getActivities() {
    return getActivitiesFromDB();
}

/**
 * Add new activity to database
 */
function addActivity($data) {
    return addActivityToDB($data);
}

/**
 * Update activity in database
 */
function updateActivity($id, $data) {
    return updateActivityInDB($id, $data);
}

/**
 * Delete activity from database
 */
function deleteActivity($id) {
    return deleteActivityFromDB($id);
}

/**
 * Get activity by ID from database
 */
function getActivityById($id) {
    return getActivityByIdFromDB($id);
}

/**
 * Get activities by status from database
 */
function getActivitiesByStatus($status) {
    $pdo = getDatabaseConnection();
    if (!$pdo) return [];
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM activities WHERE status = :status ORDER BY activity_date DESC");
        $stmt->execute([':status' => $status]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get activities by status: " . $e->getMessage());
        return [];
    }
}

/**
 * Get featured activities from database
 */
function getFeaturedActivities() {
    $pdo = getDatabaseConnection();
    if (!$pdo) return [];
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM activities WHERE featured = 1 ORDER BY activity_date DESC");
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get featured activities: " . $e->getMessage());
        return [];
    }
}

/**
 * Get activities statistics from database
 */
function getActivitiesStats() {
    return getActivitiesStatsFromDB();
}

/**
 * Upload activity image
 */
function uploadActivityImage($file) {
    $uploadDir = __DIR__ . '/../uploads/activities/';
    
    // Create upload directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    // Check file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'activity_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return 'uploads/activities/' . $filename;
    }
    
    return false;
}

// Legacy file-based functions for backward compatibility (if needed)
function initializeActivitiesFile() {
    // No longer needed - database initialization is handled in db_activities.php
    return true;
}

function getActivitiesFile() {
    // No longer used
    return null;
}

function saveActivities($activities) {
    // No longer used - activities are saved directly to database
    return true;
}
?>
