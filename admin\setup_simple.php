<?php
/**
 * Simple Database Initialization using MySQLi
 */

echo "<h2>تهيئة قاعدة البيانات</h2>";

try {
    // Connect to MySQL server
    $mysqli = new mysqli('localhost', 'root', '');
    
    if ($mysqli->connect_error) {
        throw new Exception("فشل الاتصال بخادم MySQL: " . $mysqli->connect_error);
    }
    
    echo "✅ تم الاتصال بخادم MySQL بنجاح<br>";
    
    // Create database
    $mysqli->query("CREATE DATABASE IF NOT EXISTS ngo_charity CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات 'ngo_charity' بنجاح<br>";
    
    // Select database
    $mysqli->select_db('ngo_charity');
    
    // Create activities table
    $createTable = "
    CREATE TABLE IF NOT EXISTS activities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        content TEXT,
        activity_date DATE,
        location VARCHAR(255),
        beneficiaries_count INT DEFAULT 0,
        budget DECIMAL(12,2) DEFAULT 0.00,
        status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
        category VARCHAR(50) DEFAULT 'general',
        featured BOOLEAN DEFAULT FALSE,
        image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($mysqli->query($createTable)) {
        echo "✅ تم إنشاء جدول 'activities' بنجاح<br>";
    } else {
        throw new Exception("فشل في إنشاء الجدول: " . $mysqli->error);
    }
    
    // Check if table is empty
    $result = $mysqli->query("SELECT COUNT(*) as count FROM activities");
    $row = $result->fetch_assoc();
    $count = $row['count'];
    
    if ($count == 0) {
        // Insert sample data
        $sampleData = [
            [
                'title' => 'توزيع المساعدات الغذائية',
                'description' => 'توزيع الطرود الغذائية على الأسر المحتاجة في الموصل',
                'content' => 'نقوم بتوزيع الطرود الغذائية التي تحتوي على مواد أساسية مثل الأرز والسكر والزيت والدقيق وغيرها من المواد الضرورية للأسر المحتاجة والفقيرة في مدينة الموصل وضواحيها.',
                'activity_date' => '2024-01-15',
                'location' => 'الموصل - أحياء مختلفة',
                'beneficiaries_count' => 150,
                'budget' => 25000.00,
                'status' => 'completed',
                'category' => 'food',
                'featured' => 1
            ],
            [
                'title' => 'حملة التطعيم المجاني',
                'description' => 'حملة تطعيم مجانية للأطفال في المناطق النائية',
                'content' => 'تنظيم حملة تطعيم مجانية بالتعاون مع وزارة الصحة لتطعيم الأطفال ضد الأمراض المعدية في المناطق النائية والمحرومة.',
                'activity_date' => '2024-02-20',
                'location' => 'قرى وأطراف الموصل',
                'beneficiaries_count' => 300,
                'budget' => 15000.00,
                'status' => 'completed',
                'category' => 'health',
                'featured' => 1
            ],
            [
                'title' => 'مشروع كفالة الأيتام',
                'description' => 'برنامج شهري لكفالة ومتابعة الأطفال الأيتام',
                'content' => 'برنامج شامل لكفالة الأيتام يشمل الدعم المالي الشهري، المتابعة التعليمية، الرعاية الصحية، والأنشطة الترفيهية والتربوية.',
                'activity_date' => '2024-01-01',
                'location' => 'مختلف أنحاء الموصل',
                'beneficiaries_count' => 75,
                'budget' => 45000.00,
                'status' => 'in_progress',
                'category' => 'social',
                'featured' => 1
            ]
        ];
        
        $insertQuery = "INSERT INTO activities (title, description, content, activity_date, location, beneficiaries_count, budget, status, category, featured) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $mysqli->prepare($insertQuery);
        
        if ($stmt) {
            foreach ($sampleData as $data) {
                $stmt->bind_param("sssssiissi", 
                    $data['title'],
                    $data['description'],
                    $data['content'],
                    $data['activity_date'],
                    $data['location'],
                    $data['beneficiaries_count'],
                    $data['budget'],
                    $data['status'],
                    $data['category'],
                    $data['featured']
                );
                $stmt->execute();
            }
            $stmt->close();
            echo "✅ تم إدراج البيانات التجريبية بنجاح (" . count($sampleData) . " نشاط)<br>";
        }
    } else {
        echo "ℹ️ الجدول يحتوي على {$count} سجل. لم يتم إدراج بيانات تجريبية<br>";
    }
    
    $mysqli->close();
    
    // Create news table and add sample data
    echo "<br><h3>إعداد جدول الأخبار</h3>";
    
    $mysqli = new mysqli('localhost', 'root', '', 'ngo_charity');
    if ($mysqli->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $mysqli->connect_error);
    }
    
    // Create news table
    $createNewsTable = "
    CREATE TABLE IF NOT EXISTS news (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        summary TEXT,
        content TEXT NOT NULL,
        news_date DATE,
        status ENUM('published', 'draft', 'scheduled') DEFAULT 'published',
        featured BOOLEAN DEFAULT FALSE,
        image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($mysqli->query($createNewsTable)) {
        echo "✅ تم إنشاء جدول 'news' بنجاح<br>";
    } else {
        throw new Exception("فشل في إنشاء جدول الأخبار: " . $mysqli->error);
    }
    
    // Check if news table is empty
    $result = $mysqli->query("SELECT COUNT(*) as count FROM news");
    $row = $result->fetch_assoc();
    $newsCount = $row['count'];
    
    if ($newsCount == 0) {
        $newsData = [
            [
                'title' => 'حملة توزيع المساعدات الشتوية تصل إلى 500 أسرة',
                'summary' => 'نجحت الرابطة في توزيع المساعدات الشتوية على 500 أسرة محتاجة في مختلف أحياء الموصل',
                'content' => 'في إطار حملتها السنوية لمساعدة الأسر المحتاجة خلال فصل الشتاء، نجحت رابطة أعن بإحسان الخيرية في توزيع المساعدات الشتوية على أكثر من 500 أسرة في مختلف أحياء مدينة الموصل. شملت المساعدات الملابس الشتوية، البطانيات، أجهزة التدفئة، والمواد الغذائية الأساسية.',
                'news_date' => '2025-01-15',
                'status' => 'published',
                'featured' => 1
            ],
            [
                'title' => 'افتتاح مركز جديد للرعاية الصحية المجانية',
                'summary' => 'افتتاح مركز طبي متخصص في تقديم الخدمات الصحية المجانية للأسر الفقيرة',
                'content' => 'افتتحت رابطة أعن بإحسان الخيرية مركزاً طبياً جديداً في حي الزهراء لتقديم الخدمات الصحية المجانية للأسر الفقيرة والمحتاجة. يضم المركز عيادات متخصصة في الطب العام، طب الأطفال، وطب النساء والولادة، بالإضافة إلى صيدلية مجانية.',
                'news_date' => '2025-02-01',
                'status' => 'published',
                'featured' => 1
            ],
            [
                'title' => 'برنامج كفالة الأيتام يحتفل بتخريج 25 طالباً',
                'summary' => 'احتفل برنامج كفالة الأيتام بتخريج 25 طالباً من المرحلة الثانوية',
                'content' => 'نظمت رابطة أعن بإحسان الخيرية حفل تخريج لـ 25 طالباً من الأيتام المكفولين في البرنامج من المرحلة الثانوية. حقق الطلاب نتائج متميزة وحصل عدد منهم على معدلات عالية تؤهلهم لدخول الجامعات العراقية.',
                'news_date' => '2025-07-20',
                'status' => 'published',
                'featured' => 0
            ]
        ];
        
        $insertQuery = "INSERT INTO news (title, summary, content, news_date, status, featured) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $mysqli->prepare($insertQuery);
        
        if ($stmt) {
            foreach ($newsData as $data) {
                $stmt->bind_param("sssssi", 
                    $data['title'],
                    $data['summary'],
                    $data['content'],
                    $data['news_date'],
                    $data['status'],
                    $data['featured']
                );
                $stmt->execute();
            }
            $stmt->close();
            echo "✅ تم إدراج البيانات التجريبية للأخبار بنجاح (" . count($newsData) . " خبر)<br>";
        }
    } else {
        echo "ℹ️ جدول الأخبار يحتوي على {$newsCount} سجل. لم يتم إدراج بيانات تجريبية<br>";
    }
    
    $mysqli->close();
    
    echo "<br><strong>🎉 تم إعداد قاعدة البيانات بنجاح!</strong><br>";
    echo "<br>يمكنك الآن:<br>";
    echo "• عرض النشاطات: <a href='../activities.php' target='_blank'>../activities.php</a><br>";
    echo "• إدارة النشاطات: <a href='activities.php' target='_blank'>activities.php</a><br>";
    echo "• إدارة الأخبار: <a href='news.php' target='_blank'>news.php</a><br>";
    echo "• لوحة التحكم: <a href='dashboard.php' target='_blank'>dashboard.php</a><br>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "تأكد من:<br>";
    echo "• تشغيل خدمة MySQL في XAMPP<br>";
    echo "• صحة بيانات الاتصال (افتراضي: root بدون كلمة مرور)<br>";
}
?>
