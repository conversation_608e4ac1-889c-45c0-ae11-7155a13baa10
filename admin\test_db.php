<?php
/**
 * Database setup and verification script
 */

require_once 'auth.php';

echo "<h2>تحقق من نظام المصادقة</h2>";

// Test database connection
$db = getAdminDatabaseConnection();

if (!$db) {
    echo "<p style='color: orange;'>⚠️ لا يوجد اتصال بقاعدة البيانات - سيتم استخدام نظام الملفات</p>";
} else {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    echo "<p>نوع الاتصال: " . $db['type'] . "</p>";
}

// Initialize authentication system
$result = createDefaultAdminUser();

if ($result) {
    echo "<p style='color: green;'>✅ تم إعداد نظام المصادقة بنجاح</p>";
} else {
    echo "<p style='color: red;'>❌ فشل في إعداد نظام المصادقة</p>";
}

// Test authentication
echo "<h3>اختبار تسجيل الدخول</h3>";
$testUser = authenticateAdmin('admin', 'admin123');

if ($testUser) {
    echo "<p style='color: green;'>✅ تم اختبار تسجيل الدخول بنجاح</p>";
    echo "<p>بيانات المستخدم:</p>";
    echo "<ul>";
    echo "<li>الاسم: " . htmlspecialchars($testUser['full_name']) . "</li>";
    echo "<li>البريد الإلكتروني: " . htmlspecialchars($testUser['email']) . "</li>";
    echo "<li>المعرف: " . $testUser['id'] . "</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ فشل اختبار تسجيل الدخول</p>";
}

// Show system status
echo "<h3>حالة النظام</h3>";

if (!$db) {
    echo "<p><strong>نظام التشغيل:</strong> ملفات JSON (Fallback)</p>";
    echo "<p><strong>موقع البيانات:</strong> admin/data/admin_users.json</p>";
    
    // Show file contents
    $users = getAdminUsers();
    echo "<h4>المستخدمون المحفوظون:</h4>";
    if (empty($users)) {
        echo "<p>لا يوجد مستخدمين</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>المعرف</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد</th><th>الدور</th><th>الحالة</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . htmlspecialchars($user['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p><strong>نظام التشغيل:</strong> قاعدة البيانات MySQL</p>";
    echo "<p><strong>قاعدة البيانات:</strong> ngo_charity</p>";
    echo "<p><strong>الجدول:</strong> admin_users</p>";
    
    // List database users
    try {
        if ($db['type'] === 'pdo') {
            $stmt = $db['connection']->query("SELECT id, username, full_name, email, role, status, created_at FROM admin_users");
            $users = $stmt->fetchAll();
        } else {
            $result = $db['connection']->query("SELECT id, username, full_name, email, role, status, created_at FROM admin_users");
            $users = $result->fetch_all(MYSQLI_ASSOC);
        }
        
        echo "<h4>المستخدمون في قاعدة البيانات:</h4>";
        if (empty($users)) {
            echo "<p>لا يوجد مستخدمين</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>المعرف</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد</th><th>الدور</th><th>الحالة</th><th>تاريخ الإنشاء</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($user['id']) . "</td>";
                echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
                echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                echo "<td>" . htmlspecialchars($user['role']) . "</td>";
                echo "<td>" . htmlspecialchars($user['status']) . "</td>";
                echo "<td>" . htmlspecialchars($user['created_at']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في جلب قائمة المستخدمين: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='login.php'>🔐 انتقال لصفحة تسجيل الدخول</a></p>";
echo "<p><a href='../index.php'>🏠 العودة للموقع الرئيسي</a></p>";
?>
