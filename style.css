/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    direction: rtl;
}

/* متغيرات الألوان */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #28a745;
    --accent-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* شريط التنقل */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    padding: 1rem 0;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.logo-link:hover {
    opacity: 0.9;
    transform: scale(1.02);
}

.nav-logo {
    width: 35px;
    height: 35px;
    object-fit: contain;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.3);
    transition: all 0.3s ease;
}

.nav-logo:hover {
    transform: scale(1.1);
    border-color: rgba(255,255,255,0.6);
}

.logo-link:hover .nav-logo {
    transform: scale(1.1) rotate(5deg);
}

.nav-title {
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.nav-menu {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.nav-link.admin-link {
    background-color: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
}

.nav-link.admin-link:hover {
    background-color: rgba(255,255,255,0.3);
}

/* المحتوى الرئيسي */
.main-content {
    min-height: calc(100vh - 140px);
}

/* قسم البطل في الصفحة الرئيسية */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.organization-logo {
    margin-bottom: 2rem;
}

.main-logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    border-radius: 50%;
    border: 3px solid rgba(255,255,255,0.3);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.1);
    padding: 10px;
}

.main-logo:hover {
    transform: scale(1.05);
    border-color: rgba(255,255,255,0.5);
}

.logo-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.logo-section h2 {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.contact-info {
    margin: 2rem 0;
}

.contact-info h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.email {
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.email a {
    color: white;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: border-color 0.3s;
}

.email a:hover {
    border-bottom-color: white;
}

.quick-links {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* قسم الترحيب */
.welcome {
    padding: 3rem 0;
    background-color: white;
}

.welcome h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 2rem;
}

.welcome p {
    text-align: center;
    font-size: 1.1rem;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

/* رأس الصفحة */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* محتوى صفحة عن الرابطة */
.about-content {
    padding: 3rem 0;
}

.about-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.about-card h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    text-align: center;
    font-size: 2rem;
}

.about-text {
    line-height: 1.8;
}

.intro {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1.5rem;
    border-radius: 10px;
}

.highlight {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.3em;
}

.location-info,
.mission,
.services,
.vision,
.values {
    margin: 2rem 0;
}

.location-info h3,
.mission h3,
.services h3,
.vision h3,
.values h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.services-list {
    list-style: none;
    margin-right: 1rem;
}

.services-list li {
    margin: 0.8rem 0;
    font-size: 1.1rem;
}

.sub-services {
    margin-right: 2rem;
    margin-top: 0.5rem;
}

.sub-services li {
    margin: 0.5rem 0;
    font-size: 1rem;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.value-item {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    border-right: 4px solid var(--primary-color);
}

.value-item h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

/* محتوى صفحة النشاطات */
.activities-content {
    padding: 3rem 0;
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.activity-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.activity-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.activity-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.activity-card p {
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.activity-details {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.tag {
    background-color: var(--primary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* قسم الإحصائيات */
.statistics {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    margin: 3rem 0;
    text-align: center;
}

.statistics h2 {
    margin-bottom: 2rem;
    font-size: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* قسم المساهمة */
.contribute {
    margin: 3rem 0;
}

.contribute-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    border: 2px solid var(--secondary-color);
}

.contribute-card h2 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.contact-methods {
    margin-top: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1.5rem;
    border-radius: 10px;
}

/* التذييل */
.footer {
    background-color: var(--dark-color);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer p {
    margin: 0.5rem 0;
}

.developer-credit {
    font-size: 0.75rem;
    opacity: 0.8;
    font-style: italic;
    margin-top: 1rem !important;
    color: #ffc107;
}

/* استجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem 20px;
    }
    
    .nav-brand {
        gap: 0.5rem;
    }
    
    .nav-logo {
        width: 30px;
        height: 30px;
    }
    
    .nav-title {
        font-size: 1rem;
    }
    
    .nav-menu {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-content h2 {
        font-size: 1.2rem;
    }
    
    .main-logo {
        width: 80px;
        height: 80px;
    }
    
    .quick-links {
        flex-direction: column;
        align-items: center;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .activities-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .values-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero {
        padding: 2rem 0;
    }
    
    .about-card,
    .activity-card,
    .contribute-card {
        padding: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    /* Responsive logos */
    .nav-brand {
        gap: 0.5rem;
    }
    
    .nav-logo {
        width: 30px;
        height: 30px;
    }
    
    .nav-title {
        font-size: 1rem;
    }
    
    .main-logo {
        width: 80px;
        height: 80px;
    }
    
    .login-logo {
        width: 50px;
        height: 50px;
    }
}

/* تحسينات إضافية */
.icon {
    display: inline-block;
    margin-left: 0.5rem;
}

/* تأثيرات التمرير السلس */
html {
    scroll-behavior: smooth;
}

/* تحسين شكل الروابط */
a {
    transition: all 0.3s ease;
}

/* تحسين شكل النصوص */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
}

/* تحسين المساحات */
section {
    margin-bottom: 2rem;
}

/* تحسينات خاصة بصفحة النشاطات */
.featured-activities {
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
}

.featured-activities h2 {
    color: white;
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.activity-card.featured {
    border: 3px solid #ffd700;
    background: rgba(255,255,255,0.95);
    color: #333;
    transform: scale(1.02);
}

.activity-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.activity-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.activity-card:hover .activity-image img {
    transform: scale(1.1);
}

.activity-content {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-left: 4px solid var(--primary-color);
    border-radius: 5px;
}

.tag.location {
    background: #e3f2fd;
    color: #1976d2;
}

.tag.date {
    background: #f3e5f5;
    color: #7b1fa2;
}

.tag.beneficiaries {
    background: #e8f5e8;
    color: #388e3c;
}

.tag.status {
    font-weight: bold;
}

.tag.status-completed {
    background: #d4edda;
    color: #155724;
}

.tag.status-in_progress {
    background: #fff3cd;
    color: #856404;
}

.tag.status-pending {
    background: #cce5ff;
    color: #004085;
}

.tag.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.no-activities {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.no-activities p {
    color: #6c757d;
    font-size: 1.2rem;
}

/* تحسين الإحصائيات */
.statistics {
    background: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    margin-top: 3rem;
}

.statistics h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* Simple Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    max-width: 420px;
    width: 90%;
    overflow: hidden;
}

.modal-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    text-align: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-header i {
    margin-left: 10px;
    color: #fff;
}

.modal-body {
    padding: 2rem 1.5rem;
    text-align: center;
}

.confirmation-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.activity-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    border-right: 4px solid var(--primary-color);
    text-align: right;
}

.activity-info p {
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.activity-info p:last-child {
    margin-bottom: 0;
}

.activity-info i {
    color: var(--primary-color);
    margin-left: 8px;
    width: 20px;
}

.warning-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    font-size: 0.9rem;
    text-align: center;
}

.warning-message i {
    color: #f39c12;
    margin-left: 8px;
}

.modal-footer {
    padding: 1rem 1.5rem 1.5rem;
    display: flex;
    justify-content: center;
    gap: 1rem;
    background: #f8f9fa;
}

.modal-footer .btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    min-width: 120px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-danger {
    background: #e74c3c;
    border: none;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-secondary {
    background: #6c757d;
    border: none;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    border: none;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

/* Action Buttons Styles */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    min-width: 80px;
    justify-content: center;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-sm i {
    font-size: 0.8rem;
}

/* Responsive action buttons */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 0.3rem;
    }
    
    .btn-sm {
        width: 100%;
        min-width: auto;
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }
}

/* Section Info Styles */
.section-info {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: 1px solid #90caf9;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: #0d47a1;
}

.section-info p {
    margin: 0;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-info i {
    color: #1976d2;
    font-size: 1.1rem;
}

/* Table Header Improvements */
.data-table th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding: 1rem 0.75rem;
    text-align: center;
}

.data-table th:last-child {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #0d47a1;
    font-weight: 700;
}

.data-table td:last-child {
    background: #f8f9fa;
    border-right: 3px solid #dee2e6;
}

/* Responsive */
@media (max-width: 576px) {
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-header h3 {
        font-size: 1.1rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    .modal-footer {
        flex-direction: column-reverse;
        gap: 0.8rem;
    }
    
    .modal-footer .btn {
        width: 100%;
    }
}

/* Image Modal Styles */
.image-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.image-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.modal-image {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    animation: zoomIn 0.3s ease;
}

.image-modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10000;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.image-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.image-modal-caption {
    color: white;
    font-size: 16px;
    text-align: center;
    margin-top: 15px;
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    max-width: 100%;
}

/* Clickable image effects */
.clickable-image {
    transition: all 0.3s ease;
    position: relative;
}

.clickable-image:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

.clickable-image::after {
    content: '🔍';
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 8px;
    border-radius: 50%;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.clickable-image:hover::after {
    opacity: 1;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes zoomIn {
    from { 
        transform: scale(0.5);
        opacity: 0;
    }
    to { 
        transform: scale(1);
        opacity: 1;
    }
}

/* Mobile responsive for image modal */
@media (max-width: 768px) {
    .image-modal-content {
        max-width: 95%;
        max-height: 95%;
    }
    
    .modal-image {
        max-height: 70vh;
    }
    
    .image-modal-close {
        top: -35px;
        font-size: 30px;
        width: 35px;
        height: 35px;
    }
    
    .image-modal-caption {
        font-size: 14px;
        margin-top: 10px;
        padding: 8px 15px;
    }
}
